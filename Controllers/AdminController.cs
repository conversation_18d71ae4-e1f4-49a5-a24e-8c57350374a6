﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ExcelDataReader;
using Huzur.Models;
using Huzur.Models.ViewModels;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Data.OleDb;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;
using System.Text;
using Newtonsoft.Json;
using System.Globalization;
using MySqlConnector;
using MySql.Data.MySqlClient;
using static System.Net.WebRequestMethods;
using System.Reflection.Metadata;
using System.Security.Claims;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;

namespace Huzur.Controllers
{
    [Authorize]
    public class AdminController : Controller
    {
        private HDbContext context;

        public AdminController(HDbContext _context)
        {
            context = _context;
        }

        [AllowAnonymous]
        public IActionResult Login()
        {
            return View();
        }


        [HttpPost]
        [AllowAnonymous]
        public bool Login(string passw)
        {
            if (passw == "atural61")
            {
                var claims = new List<Claim>();
                claims.Add(new Claim(ClaimTypes.Name, "huzur"));
                var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var principal = new ClaimsPrincipal(identity);
                HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(principal));
                var cookieOptions = new CookieOptions
                {
                    Expires = DateTimeOffset.Now.AddMonths(3),
                    MaxAge = TimeSpan.FromDays(90),
                    Secure = true,
                    HttpOnly = true,
                    SameSite = SameSiteMode.Lax
                };
                HttpContext.Response.Cookies.Append("loggedin", "true", cookieOptions);
                return true;
            }
            else
            {
                return false;
            }

        }

        [HttpPost]
        public IActionResult Logout()
        {
            HttpContext.Response.Cookies.Delete("loggedin");
            HttpContext.Response.Cookies.Delete("sessionid");
            return Redirect("/account/login");
        }

        public IActionResult Index()
        {
            var model = new AdminProductsModel()
            {
                HProducts = context.HProducts.ToList(),
                HProductInfos = context.HProductInfos.ToList(),
                HCatalogs = context.HCatalogs.ToList(),
                HSliders = context.HSliders.ToList()
            };
            return View(model);
        }

        public IActionResult Products()
        {
            var model = new AdminProductsModel()
            {
                HProducts = context.HProducts.ToList(),
                HProductInfos = context.HProductInfos.ToList(),
                HCategories = context.HCategories.ToList(),
            };

            return View(model);
        }

        public IActionResult ProductList()
        {
            var model = new AdminProductsModel()
            {
                HProducts = context.HProducts.ToList(),
                HProductInfos = context.HProductInfos.ToList()
            };
            return View(model);
        }

        public IActionResult Slider()
        {
            var model = new AdminProductsModel()
            {
                HCategories = context.HCategories.ToList(),
                HSliders = context.HSliders.ToList()
            };
            return View(model);
        }

        public IActionResult Catalog()
        {
            var catalogs = context.HCatalogs.ToList();
            return View(catalogs);
        }

        [HttpPost]
        public bool AddProduct(HProduct product, List<IFormFile> files, IFormFile _sketch)
        {
            try
            {
                if (product.CategoryId > 8)
                    product.mainct = HProduct.maincat.Kitchen;
                else if (product.CategoryId == 8)
                    product.mainct = HProduct.maincat.Bedroom;
                else if (product.CategoryId == 9)
                    product.mainct = HProduct.maincat.Bathroom;

                //product add
                var prdct = new HProduct();
                prdct = product;
                var _sketchname = UploadSketch(_sketch, prdct.id);
                product.sketch = _sketchname;
                product.date = DateTime.Now.ToString("dd/MM/yyyy");
                product.updatedDate = DateTime.Now.ToString("dd/MM/yyyy");
                context.HProducts.Add(product);
                context.SaveChanges();
                // image add
                for (int i = 0; i < files.Count(); i++)
                {
                    var img = new HImage();
                    img.ProductId = prdct.id;
                    var _filename = UploadImage(files[i], prdct.id);
                    img.name = _filename;
                    context.HImages.Add(img);
                    context.SaveChanges();
                }
                //// info add
                //var info = productInfo;
                //productInfo.hcode = product.hcode;
                //productInfo.date = DateTime.Now.ToString("dd/MM/yyyy");
                //productInfo.updatedDate = DateTime.Now.ToString("dd/MM/yyyy");
                //productInfo.ProductId = prdct.id;
                //context.HProductInfos.Add(info);
                //context.SaveChanges();

                return true;
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                ex.exinner = e.InnerException.ToString();
                context.Exceptions.Add(ex);
                context.SaveChanges();
                return false;
            }

        }

        [HttpPost]
        public bool UpdateProduct(HProduct product, List<IFormFile> files, IFormFile _sketch)
        {
            try
            {
                var _product = context.HProducts.FirstOrDefault(c => c.id == product.id);

                if (product.CategoryId < 8)
                    _product.mainct = HProduct.maincat.Kitchen;
                else if (product.CategoryId == 8)
                    _product.mainct = HProduct.maincat.Bedroom;
                else if (product.CategoryId == 9)
                    _product.mainct = HProduct.maincat.Bathroom;


                // skecht add
                if (_sketch != null)
                {
                    var _sketchname = UploadSketch(_sketch, _product.id);
                    _product.sketch = _sketchname;
                }

                // image add
                if (files.Count() != 0)
                {
                    for (int i = 0; i < files.Count(); i++)
                    {
                        var img = new HImage();
                        img.ProductId = _product.id;
                        var _filename = UploadImage(files[i], _product.id);
                        img.name = _filename;
                        context.HImages.Add(img);
                        context.SaveChanges();
                    }
                }


                //product update
                foreach (var prop in typeof(HProduct).GetProperties())
                {
                    var value = prop.GetValue(product);
                    if (value != null)
                    {
                        prop.SetValue(_product, value);
                    }
                }

                _product.updatedDate = DateTime.Now.ToString("dd/MM/yyyy");
                context.HProducts.Update(_product);
                context.SaveChanges();


                return true;
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                context.Exceptions.Add(ex);
                context.SaveChanges();
                return false;
            }

        }

        public object GetProductInfo(int id)
        {
            var prd = context.HProducts.Find(id);
            var info = context.HProductInfos.Where(c => c.ProductId == prd.id).First();

            var model = new List<object>();
            model.Add(prd.name);
            model.Add(prd.nameUS);
            model.Add(prd.price);
            model.Add(prd.priceUSD);
            model.Add(prd.description);
            model.Add(prd.descriptionUS);
            model.Add(prd.setuplink);
            model.Add(prd.promolink);
            model.Add(prd.sketch);
            model.Add(prd.Images);
            model.Add(prd.colors);
            model.Add(prd.CategoryId);

            return model;
        }

        public HSlider GetSlider(int id)
        {
            try
            {
                var prd = context.HSliders.FirstOrDefault(c => c.id == id);

                return prd;
            }
            catch
            {
                var prd = new HSlider();
                prd.id = 0;
                return prd;
            }
        }

        [HttpPost]
        public HSlider AddSlider(string lowerText, string upperText, string midText, int catId, IFormFile _file)
        {
            try
            {
                var prd = new HSlider();
                prd.upperText = upperText;
                prd.lowerText = lowerText;
                prd.midText = midText;
                prd.CategoryId = catId;
                prd.date = DateTime.Now.ToString("dd/MM/yyyy");
                prd.updatedDate = DateTime.Now.ToString("dd/MM/yyyy");
                prd.isSliderActive = false;

                if (_file != null)
                {
                    string uniqueFileName = "";
                    var ext = new FileInfo(_file.FileName).Extension;

                    string uploadsFolder = Path.Combine("wwwroot/images");
                    uniqueFileName = "huzurSlider" + Guid.NewGuid().ToString().Substring(0, 10) + ext;
                    string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        _file.CopyTo(fileStream);
                    }

                    prd.sliderImage = uniqueFileName;
                }

                context.HSliders.Add(prd);
                context.SaveChanges();

                return prd;
            }
            catch (Exception e)
            {
                var prd = new HSlider();
                prd.id = 0;
                return prd;
            }
        }

        [HttpPost]
        public string UpdateSlider(int id, string lowerText, string upperText, string midText, int catId, IFormFile _file)
        {
            try
            {
                var prd = context.HSliders.Find(id);
                prd.upperText = upperText;
                prd.lowerText = lowerText;
                prd.midText = midText;
                prd.CategoryId = catId;
                prd.updatedDate = DateTime.Now.ToString("dd/MM/yyyy");

                string uniqueFileName = "";
                if (_file != null)
                {
                    var ext = new FileInfo(_file.FileName).Extension;

                    string uploadsFolder = Path.Combine("wwwroot/images");
                    uniqueFileName = "huzurSlider" + Guid.NewGuid().ToString().Substring(0, 10) + ext;
                    string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        _file.CopyTo(fileStream);
                    }

                    prd.sliderImage = uniqueFileName;
                }

                context.HSliders.Update(prd);
                context.SaveChanges();

                return uniqueFileName;
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                context.Exceptions.Add(ex);
                context.SaveChanges();
                return "false";
            }
        }

        [HttpPost]
        public bool ActiveSlider(int id, bool isActive)
        {
            try
            {
                var prd = context.HSliders.FirstOrDefault(c => c.id == id);
                prd.isSliderActive = isActive;
                context.HSliders.Update(prd);
                context.SaveChanges();

                return true;
            }
            catch
            {
                return false;
            }
        }

        [HttpPost]
        public bool RemoveSlider(int id)
        {
            try
            {
                var prd = context.HSliders.FirstOrDefault(c => c.id == id);

                context.HSliders.Remove(prd);
                context.SaveChanges();

                return true;
            }
            catch
            {
                return false;
            }
        }

        [HttpPost]
        public bool DelProduct(int id)
        {
            try
            {
                var ct = context.HProducts.Find(id);
                context.HProducts.Remove(ct);
                context.SaveChanges();
                return true;
            }
            catch
            {
                return false;
            }

        }


        private string UploadImage(IFormFile _file, int productId)
        {
            try
            {
                string uniqueFileName = "";
                var ext = new FileInfo(_file.FileName).Extension;

                string uploadsFolder = Path.Combine("wwwroot/images");
                uniqueFileName = "huzur" + Guid.NewGuid().ToString().Substring(0, 10) + ext;
                string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    _file.CopyTo(fileStream);
                }

                return uniqueFileName;
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                context.Exceptions.Add(ex);
                context.SaveChanges();
                return "error";
            }

        }

        private string UploadSketch(IFormFile _file, int productId)
        {
            try
            {
                string uniqueFileName = "";
                var ext = new FileInfo(_file.FileName).Extension;

                string uploadsFolder = Path.Combine("wwwroot/sketches");
                uniqueFileName = "huzur" + Guid.NewGuid().ToString().Substring(0, 10) + ext;
                string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    _file.CopyTo(fileStream);
                }

                return uniqueFileName;
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                ex.exinner = e.InnerException.ToString();
                context.Exceptions.Add(ex);
                context.SaveChanges();
                return "error";
            }

        }

        public object AddCatalog(string filename, IFormFile _file)
        {
            try
            {
                string uniqueFileName = "";
                var ext = new FileInfo(_file.FileName).Extension;

                string uploadsFolder = Path.Combine("wwwroot/catalogs");
                uniqueFileName = "huzur" + Guid.NewGuid().ToString().Substring(0, 10) + ext;
                string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    _file.CopyTo(fileStream);
                }

                var catalog = new HCatalog()
                {
                    name = filename,
                    date = DateTime.Now.ToString("dd/MM/yyyy"),
                    link = uniqueFileName,
                    size = _file.Length.ToString()
                };
                context.HCatalogs.Add(catalog);
                context.SaveChanges();

                return catalog;
            }
            catch
            {
                return false;
            }

        }

        [HttpPost]
        public bool DelCatalog(int id)
        {
            try
            {
                var ct = context.HCatalogs.Find(id);
                context.HCatalogs.Remove(ct);
                context.SaveChanges();
                return true;
            }
            catch
            {
                return false;
            }

        }

        public void ReadExcelFile(bool ok, string filePath, IFormFile _file)
        {
            try
            {
                filePath = UploadExcel(_file);
                if (ok)
                {
                    using (var stream = System.IO.File.Open(filePath, FileMode.Open, FileAccess.Read))
                    {
                        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                        using (var reader = ExcelReaderFactory.CreateOpenXmlReader(stream))
                        {
                            var result = reader.AsDataSet(new ExcelDataSetConfiguration()
                            {
                                ConfigureDataTable = (data) => new ExcelDataTableConfiguration()
                                {
                                    UseHeaderRow = true
                                }
                            });
                            ConvertDataTableToMySql(result.Tables[0], "HProductInfos");
                        }
                    }

                    var xls = new HExcelF()
                    {
                        name = filePath,
                        date = DateTime.Now.ToString("dd/MM/yyyy")
                    };
                    context.HExcelF.Add(xls);
                    context.SaveChanges();

                }
                else
                {
                    using (var stream = System.IO.File.Open(filePath, FileMode.Open, FileAccess.Read))
                    {
                        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                        using (var reader = ExcelReaderFactory.CreateOpenXmlReader(stream))
                        {
                            var result = reader.AsDataSet(new ExcelDataSetConfiguration()
                            {
                                ConfigureDataTable = (data) => new ExcelDataTableConfiguration()
                                {
                                    UseHeaderRow = true
                                }
                            });
                            ConvertDataTableToMySql(result.Tables[0], "HProducts");
                        }
                    }
                }
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                context.Exceptions.Add(ex);
                context.SaveChanges();
            }
        }

        private string UploadExcel(IFormFile _file)
        {
            try
            {
                string uniqueFileName = "";
                var ext = new FileInfo(_file.FileName).Extension;

                string uploadsFolder = Path.Combine("wwwroot/exceltables");
                uniqueFileName = "huzur" + Guid.NewGuid().ToString().Substring(0, 10) + ext;
                string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    _file.CopyTo(fileStream);
                }

                return "wwwroot/exceltables/" + uniqueFileName;
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                ex.exinner = e.InnerException.ToString();
                context.Exceptions.Add(ex);
                context.SaveChanges();
                return "error";
            }

        }

        public void ConvertDataTableToMySql(DataTable table, string tableName)
        {
            var _id = 171;
            var con = new MySqlConnection("Server=n1nlmysql17plsk.secureserver.net;Port=3306;Database=huzur;Uid=remzi;Pwd=*******;AllowUserVariables=True;");
            con.Open();
            var sql = new StringBuilder();
            if (tableName == "HProductInfos")
            {
                foreach (DataRow row in table.Rows)
                {
                    //var columns = new List<string>();
                    var columnsNames = "";
                    //var parameters = new List<MySqlParameter>();
                    var parameterNames = new List<string>();
                    foreach (DataColumn col in table.Columns)
                    {
                        var parameter = new MySqlParameter($"@{col.ColumnName}", row[col.ColumnName]);
                        parameterNames.Add(parameter.Value.ToString());
                    }
                    var command = new MySqlCommand($"INSERT INTO `HProductInfos` (`id`, `hcode`, `wdh`, `basket`, `c`, `volume`, `weight`, `date`, `updatedDate`, `ProductId`) VALUES (NULL,'{parameterNames[0]}','{parameterNames[1]}','{parameterNames[2]}','{parameterNames[3]}','{parameterNames[4]}','{parameterNames[5]}','{parameterNames[6].Substring(0, 10)}','{parameterNames[7].Substring(0, 10)}','{parameterNames[8]}' );", con);
                    command.ExecuteNonQuery();
                    context.SaveChanges();
                }
            }
            else
            {
                foreach (DataRow row in table.Rows)
                {
                    var image = "";
                    var image2 = "";
                    var image3 = "";

                    var parameterNames = new List<string>();
                    foreach (DataColumn col in table.Columns)
                    {
                        var parameter = new MySqlParameter($"@{col.ColumnName}", row[col.ColumnName]);
                        parameterNames.Add(parameter.Value.ToString());

                        if (col.ColumnName == "image")
                            image = parameter.Value.ToString();
                        if (col.ColumnName == "image2")
                            image2 = parameter.Value.ToString();
                        if (col.ColumnName == "image3")
                            image3 = parameter.Value.ToString();

                    }


                    var command = new MySqlCommand($"INSERT INTO `HProducts` (`id`, `name`, `nameUS`, `nameAR`,`price`,`priceUSD`,`priceEUR`,`mainct`,`date`,`updatedDate`,`CategoryId`,`colors`) VALUES (NULL,'{parameterNames[0]}','{parameterNames[1]}','{parameterNames[2]}','{parameterNames[3]}','{parameterNames[4]}','{parameterNames[5]}','{parameterNames[6]}','{parameterNames[7].Substring(0, 10)}','{parameterNames[8].Substring(0, 10)}','{parameterNames[9]}','{parameterNames[10]}');", con);
                    command.ExecuteNonQuery();
                    context.SaveChanges();

                    var img = new HImage()
                    {
                        ProductId = _id,
                        name = image
                    };
                    var img2 = new HImage()
                    {
                        ProductId = _id,
                        name = image2
                    };
                    var img3 = new HImage()
                    {
                        ProductId = _id,
                        name = image3
                    };

                    context.HImages.Add(img);
                    context.HImages.Add(img2);
                    context.HImages.Add(img3);
                    context.SaveChanges();
                    _id++;
                }
            }

            con.Close();
        }

        [HttpPost]
        public bool DelXLS(int id)
        {
            try
            {
                var ct = context.HExcelF.Find(id);
                context.HExcelF.Remove(ct);
                context.SaveChanges();
                return true;
            }
            catch
            {
                return false;
            }

        }
    }
}

