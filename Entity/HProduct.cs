﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Huzur.Models
{
    public class HProduct
    {
        public HProduct()
        {
        }

        public int id { get; set; }
        public string name { get; set; }
        public string nameUS { get; set; }
        public string nameAR { get; set; }
        public string description { get; set; }
        public string descriptionUS { get; set; }
        public string descriptionAR { get; set; }
        public string sketch { get; set; }
        public bool popular { get; set; }
        public maincat mainct { get; set; }
        public int saleRate { get; set; }
        public string colors { get; set; }
        public string setuplink { get; set; }
        public string promolink { get; set; }
        public int price { get; set; }
        public int priceUSD { get; set; }
        public int priceEUR { get; set; }
        public string date { get; set; }
        public string updatedDate { get; set; }

        [JsonIgnore]
        public HCategory Category { get; set; }
        public int CategoryId { get; set; }

        public virtual ICollection<HImage> Images { get; set; }
        public ICollection<HProductInfo> ProductInfos { get; set; }


        public enum maincat
        {
            All,
            Kitchen,
            Bedroom,
            Bathroom
        }


    }
}

