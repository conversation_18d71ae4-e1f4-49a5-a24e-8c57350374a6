﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Huzur.Models
{
    public class HProductInfo
    {
        public HProductInfo()
        {
        }

        public int id { get; set; }
        public string hcode { get; set; }
        public string wdh { get; set; }
        public int basket { get; set; }
        public int c { get; set; }
        public double volume { get; set; }
        public double weight { get; set; }
        public string date { get; set; } = string.Empty;
        public string updatedDate { get; set; } = string.Empty;


        [JsonIgnore]
        public HProduct Product { get; set; }
        public int ProductId { get; set; }
    }
}

