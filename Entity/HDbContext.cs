﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Huzur.Models
{
    public class HDbContext : DbContext
    {
        private readonly ILogger<HDbContext>? _logger;

        public HDbContext(DbContextOptions<HDbContext> options) : base(options)
        {
        }

        public HDbContext(DbContextOptions<HDbContext> options, ILogger<HDbContext> logger) : base(options)
        {
            _logger = logger;
        }

        public DbSet<HProduct> HProducts { get; set; } = null!;
        public DbSet<HCategory> HCategories { get; set; } = null!;
        public DbSet<HProductInfo> HProductInfos { get; set; } = null!;
        public DbSet<HImage> HImages { get; set; } = null!;
        public DbSet<HExcelF> HExcelF { get; set; } = null!;
        public DbSet<HCatalog> HCatalogs { get; set; } = null!;
        public DbSet<HSlider> HSliders { get; set; } = null!;
        public DbSet<Exceptions> Exceptions { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure PostgreSQL specific settings
            modelBuilder.HasDefaultSchema("public");

            // Product-Image relationship
            modelBuilder.Entity<HProduct>()
                .HasMany(p => p.Images)
                .WithOne()
                .HasForeignKey(img => img.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            // Product-ProductInfo relationship
            modelBuilder.Entity<HProduct>()
                .HasMany(p => p.ProductInfos)
                .WithOne()
                .HasForeignKey(info => info.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            // Add indexes for better performance
            modelBuilder.Entity<HProduct>()
                .HasIndex(p => p.CategoryId)
                .HasDatabaseName("IX_HProducts_CategoryId");

            modelBuilder.Entity<HProduct>()
                .HasIndex(p => p.popular)
                .HasDatabaseName("IX_HProducts_Popular");

            modelBuilder.Entity<HProduct>()
                .HasIndex(p => p.name)
                .HasDatabaseName("IX_HProducts_Name");

            modelBuilder.Entity<HCategory>()
                .HasIndex(c => c.name)
                .HasDatabaseName("IX_HCategories_Name");

            // Configure string lengths for PostgreSQL
            modelBuilder.Entity<HProduct>()
                .Property(p => p.name)
                .HasMaxLength(500);

            modelBuilder.Entity<HProduct>()
                .Property(p => p.nameUS)
                .HasMaxLength(500);

            modelBuilder.Entity<HProduct>()
                .Property(p => p.nameAR)
                .HasMaxLength(500);

            modelBuilder.Entity<HCategory>()
                .Property(c => c.name)
                .HasMaxLength(200);
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                _logger?.LogWarning("DbContext is being configured without dependency injection. This should only happen during design-time operations.");
            }

            // Enable query splitting for better performance with related data (EF Core 5.0+)
            // optionsBuilder.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);

            // Configure connection resilience for PostgreSQL
            optionsBuilder.EnableServiceProviderCaching();
            optionsBuilder.EnableSensitiveDataLogging(false); // Set to true only in development
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                return await base.SaveChangesAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error occurred while saving changes to database");
                throw;
            }
        }

        public override int SaveChanges()
        {
            try
            {
                return base.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error occurred while saving changes to database");
                throw;
            }
        }
    }
}


