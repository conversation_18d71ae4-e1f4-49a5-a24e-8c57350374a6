﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Huzur.Models
{
    public class HSlider
    {
        public HSlider()
        {
        }

        public int id { get; set; }
        public string sliderImage { get; set; }
        public bool isSliderActive { get; set; }
        public string date { get; set; }
        public string updatedDate { get; set; }

        public string upperText { get; set; }
        public string midText { get; set; }
        public string lowerText { get; set; }

        public string upperTextEn { get; set; }
        public string midTextEn { get; set; }
        public string lowerTextEn { get; set; }

        public string upperTextAr { get; set; }
        public string midTextAr { get; set; }
        public string lowerTextAr { get; set; }


        [JsonIgnore]
        public HCategory Category { get; set; }
        public int CategoryId { get; set; }
    }
}

