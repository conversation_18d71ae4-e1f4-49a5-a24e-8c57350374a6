{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"HuzurAksesuar/1.0.0": {"dependencies": {"ExcelDataReader": "3.6.0", "ExcelDataReader.DataSet": "3.6.0", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "6.0.33", "Microsoft.EntityFrameworkCore.Design": "6.0.33", "Npgsql.EntityFrameworkCore.PostgreSQL": "6.0.29", "Swashbuckle.AspNetCore": "6.8.1"}, "runtime": {"HuzurAksesuar.dll": {}}}, "ExcelDataReader/3.6.0": {"runtime": {"lib/netstandard2.0/ExcelDataReader.dll": {"assemblyVersion": "3.6.0.0", "fileVersion": "3.6.0.0"}}}, "ExcelDataReader.DataSet/3.6.0": {"dependencies": {"ExcelDataReader": "3.6.0"}, "runtime": {"lib/netstandard2.0/ExcelDataReader.DataSet.dll": {"assemblyVersion": "3.6.0.0", "fileVersion": "3.6.0.0"}}}, "Humanizer.Core/2.8.26": {"runtime": {"lib/netstandard2.0/Humanizer.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.26.1919"}}}, "Microsoft.AspNetCore.JsonPatch/6.0.33": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3324.37905"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/6.0.33": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "6.0.33", "Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3324.37905"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/6.0.33": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.33", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.33", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.Logging": "6.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3324.36105"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.33": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3324.36105"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.33": {}, "Microsoft.EntityFrameworkCore.Design/6.0.33": {"dependencies": {"Humanizer.Core": "2.8.26", "Microsoft.EntityFrameworkCore.Relational": "6.0.33"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3324.36105"}}}, "Microsoft.EntityFrameworkCore.Relational/6.0.33": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.33", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3324.36105"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1022.47605"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.OpenApi/1.6.14": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.14.0", "fileVersion": "1.6.14.0"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.2.22727"}}}, "Npgsql/6.0.11": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Npgsql.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.29": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.33", "Microsoft.EntityFrameworkCore.Abstractions": "6.0.33", "Microsoft.EntityFrameworkCore.Relational": "6.0.33", "Npgsql": "6.0.11"}, "runtime": {"lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Swashbuckle.AspNetCore/6.8.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.8.1", "Swashbuckle.AspNetCore.SwaggerGen": "6.8.1", "Swashbuckle.AspNetCore.SwaggerUI": "6.8.1"}}, "Swashbuckle.AspNetCore.Swagger/6.8.1": {"dependencies": {"Microsoft.OpenApi": "1.6.14"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.1.756"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.8.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.8.1"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.1.756"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.8.1": {"runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.1.756"}}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1523.11507"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}}}, "libraries": {"HuzurAksesuar/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ExcelDataReader/3.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-eXyxTwG7EEWpgQX3juZ2KcndqTqm/ydx9GXS2lUiMJU54U4h5eXdygRtVgINv0t60ezPWEb+lL7uM7Q+wm5BBA==", "path": "exceldatareader/3.6.0", "hashPath": "exceldatareader.3.6.0.nupkg.sha512"}, "ExcelDataReader.DataSet/3.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-miW3sWmPXnLM38YadRIfnhL4W4QZajpS6Q/OqsOpFJU5ct8h+M/fMpbGMm5INtsMP0Y1k0WK0stIfSW3RP31Xg==", "path": "exceldatareader.dataset/3.6.0", "hashPath": "exceldatareader.dataset.3.6.0.nupkg.sha512"}, "Humanizer.Core/2.8.26": {"type": "package", "serviceable": true, "sha512": "sha512-OiKusGL20vby4uDEswj2IgkdchC1yQ6rwbIkZDVBPIR6al2b7n3pC91elBul9q33KaBgRKhbZH3+2Ur4fnWx2A==", "path": "humanizer.core/2.8.26", "hashPath": "humanizer.core.2.8.26.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/6.0.33": {"type": "package", "serviceable": true, "sha512": "sha512-13JOBUHo+ERlYrpO6TnTF8VRlaBynYS0NPSq5lgPVCxSMohj+ktR2veQiswkMqH7FvoJFSthA+E2Xo3vHSOioQ==", "path": "microsoft.aspnetcore.jsonpatch/6.0.33", "hashPath": "microsoft.aspnetcore.jsonpatch.6.0.33.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/6.0.33": {"type": "package", "serviceable": true, "sha512": "sha512-G7wQQW0Blc/1yhDgrJxyqF+813UjtJ1uSbIKggephkr5SK2aU1u/VPQsVzqfuICuxDJ4RC5BA7zYJsjGkrcMdg==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/6.0.33", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.6.0.33.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.33": {"type": "package", "serviceable": true, "sha512": "sha512-pPfX/pnnr+decWaBkGA43PwNTYMf4vhdk7fTLroHQF8d1LDjtj+C7sar2ogchKLzpIx5GjhptabJYjSxz/BGmA==", "path": "microsoft.entityframeworkcore/6.0.33", "hashPath": "microsoft.entityframeworkcore.6.0.33.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.33": {"type": "package", "serviceable": true, "sha512": "sha512-8oufYRCOtzo/sLFUQ5nzh/R9Qn026fyjc6vgdMMGMW3AHdWj0djbQeVZf0np8eGjihRDtfFG2F8ckTphkOBAzQ==", "path": "microsoft.entityframeworkcore.abstractions/6.0.33", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.33.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.33": {"type": "package", "serviceable": true, "sha512": "sha512-MXIRItpxrFCccOBB0thHjRxKuA06w88qE8qa7A/eo6cWx6BOfScEe35+enH9T5eYUwCfW0CcneyP3v7TRKedTQ==", "path": "microsoft.entityframeworkcore.analyzers/6.0.33", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.33.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/6.0.33": {"type": "package", "serviceable": true, "sha512": "sha512-il6DvyJ++C6GLAfQAaDK2FAy2DTKoAzBN1a9pSm0Lps7U2rcjFJCyPNGxwQgN2S7dAspOqnwRgpSAzRLFbHTOw==", "path": "microsoft.entityframeworkcore.design/6.0.33", "hashPath": "microsoft.entityframeworkcore.design.6.0.33.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.33": {"type": "package", "serviceable": true, "sha512": "sha512-PlnRpPNbY/jGs5RYfqTce5CBafF/hDPXnea2hE80huJdv1+JThVRWXOc6WCaJ+Jmd2420QeJs0ouI9mEr19kUA==", "path": "microsoft.entityframeworkcore.relational/6.0.33", "hashPath": "microsoft.entityframeworkcore.relational.6.0.33.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "path": "microsoft.extensions.caching.memory/6.0.1", "hashPath": "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vWXPg3HJQIpZkENn1KWq8SfbqVujVD7S7vIAyFXXqK5xkf1Vho+vG0bLBCHxU36lD1cLLtmGpfYf0B3MYFi9tQ==", "path": "microsoft.extensions.dependencyinjection/6.0.1", "hashPath": "microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.14": {"type": "package", "serviceable": true, "sha512": "sha512-tTaBT8qjk3xINfESyOPE2rIellPvB7qpVqiWiyA/lACVvz+xOGiXhFUfohcx82NLbi5avzLW0lx+s6oAqQijfw==", "path": "microsoft.openapi/1.6.14", "hashPath": "microsoft.openapi.1.6.14.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Npgsql/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-ZPpjFpW0m71pqAMIpUdVZUY74TCTqCXrytbFsMvm8IuAadpfV9K1lwphuyDBKCsc3w1i7TH+aevHQ6TZ+b0ZMw==", "path": "npgsql/6.0.11", "hashPath": "npgsql.6.0.11.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.29": {"type": "package", "serviceable": true, "sha512": "sha512-GCUgp/4ZltsysSRJUpvpQoFCRJ6OciL5/xZePOpZ8bekthAbLGW3emYaKf2V+1mNXUm9LhCZI7T0ib3I8yNV3A==", "path": "npgsql.entityframeworkcore.postgresql/6.0.29", "hashPath": "npgsql.entityframeworkcore.postgresql.6.0.29.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-JN6ccH37QKtNOwBrvSxc+jBYIB+cw6RlZie2IKoJhjjf6HzBH+2kPJCpxmJ5EHIqmxvq6aQG+0A8XklGx9rAxA==", "path": "swashbuckle.aspnetcore/6.8.1", "hashPath": "swashbuckle.aspnetcore.6.8.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-eOkdM4bsWBU5Ty3kWbyq5O9L+05kZT0vOdGh4a92vIb/LLQGQTPLRHXuJdnUBNIPNC8XfKWfSbtRfqzI6nnbqw==", "path": "swashbuckle.aspnetcore.swagger/6.8.1", "hashPath": "swashbuckle.aspnetcore.swagger.6.8.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-TjBPxsN0HeJzxEXZYeDXBNNMSyhg+TYXtkbwX+Cn8GH/y5ZeoB/chw0p71kRo5tR2sNshbKwL24T6f9pTF9PHg==", "path": "swashbuckle.aspnetcore.swaggergen/6.8.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.8.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-lpEszYJ7vZaTTE5Dp8MrsbSHrgDfjhDMjzW1qOA1Xs1Dnj3ZRBJAcPZUTsa5Bva+nLaw91JJ8OI8FkSg8hhIyA==", "path": "swashbuckle.aspnetcore.swaggerui/6.8.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.8.1.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}}}