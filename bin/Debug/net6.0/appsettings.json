﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Microsoft.EntityFrameworkCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=huzurdb;Username=postgres;Password=your_password_here;Include Error Detail=true;Pooling=true;Minimum Pool Size=1;Maximum Pool Size=20;"
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://0.0.0.0:5000"
      }
    }
  },
  "Swagger": {
    "Title": "HuzurAksesuar API",
    "Version": "v1",
    "Description": "Modernized .NET 6 Web API with PostgreSQL and optimized performance"
  }
}

