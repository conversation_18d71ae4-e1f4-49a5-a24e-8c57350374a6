{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1/linux-x64", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "RELEASE", "NETCOREAPP", "NETCOREAPP3_1", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "languageVersion": "8.0", "platform": "x64", "allowUnsafe": false, "warningsAsErrors": false, "optimize": true, "keyFile": "", "emitEntryPoint": true, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v3.1": {"HuzurAksesuar/1.0.0": {"dependencies": {"ExcelDataReader": "3.1.0", "ExcelDataReader.DataSet": "3.1.0", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "3.1.0", "Pomelo.EntityFrameworkCore.MySql": "3.1.0", "Swashbuckle.AspNetCore": "5.6.3", "Microsoft.AspNetCore.Antiforgery": "*******", "Microsoft.AspNetCore.Authentication.Abstractions": "*******", "Microsoft.AspNetCore.Authentication.Cookies": "*******", "Microsoft.AspNetCore.Authentication.Core": "*******", "Microsoft.AspNetCore.Authentication": "*******", "Microsoft.AspNetCore.Authentication.OAuth": "*******", "Microsoft.AspNetCore.Authorization": "*******", "Microsoft.AspNetCore.Authorization.Policy": "*******", "Microsoft.AspNetCore.Components.Authorization": "*******", "Microsoft.AspNetCore.Components": "*******", "Microsoft.AspNetCore.Components.Forms": "*******", "Microsoft.AspNetCore.Components.Server": "*******", "Microsoft.AspNetCore.Components.Web": "*******", "Microsoft.AspNetCore.Connections.Abstractions": "*******", "Microsoft.AspNetCore.CookiePolicy": "*******", "Microsoft.AspNetCore.Cors": "*******", "Microsoft.AspNetCore.Cryptography.Internal": "*******", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "*******", "Microsoft.AspNetCore.DataProtection.Abstractions": "*******", "Microsoft.AspNetCore.DataProtection": "*******", "Microsoft.AspNetCore.DataProtection.Extensions": "*******", "Microsoft.AspNetCore.Diagnostics.Abstractions": "*******", "Microsoft.AspNetCore.Diagnostics": "*******", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "*******", "Microsoft.AspNetCore": "*******", "Microsoft.AspNetCore.HostFiltering": "*******", "Microsoft.AspNetCore.Hosting.Abstractions": "*******", "Microsoft.AspNetCore.Hosting": "*******", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "*******", "Microsoft.AspNetCore.Html.Abstractions": "*******", "Microsoft.AspNetCore.Http.Abstractions": "*******", "Microsoft.AspNetCore.Http.Connections.Common": "*******", "Microsoft.AspNetCore.Http.Connections": "*******", "Microsoft.AspNetCore.Http": "*******", "Microsoft.AspNetCore.Http.Extensions": "*******", "Microsoft.AspNetCore.Http.Features": "*******", "Microsoft.AspNetCore.HttpOverrides": "*******", "Microsoft.AspNetCore.HttpsPolicy": "*******", "Microsoft.AspNetCore.Identity": "*******", "Microsoft.AspNetCore.Localization": "*******", "Microsoft.AspNetCore.Localization.Routing": "*******", "Microsoft.AspNetCore.Metadata": "*******", "Microsoft.AspNetCore.Mvc.Abstractions": "*******", "Microsoft.AspNetCore.Mvc.ApiExplorer": "*******", "Microsoft.AspNetCore.Mvc.Core": "*******", "Microsoft.AspNetCore.Mvc.Cors": "*******", "Microsoft.AspNetCore.Mvc.DataAnnotations": "*******", "Microsoft.AspNetCore.Mvc": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Json": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "*******", "Microsoft.AspNetCore.Mvc.Localization": "*******", "Microsoft.AspNetCore.Mvc.Razor": "*******", "Microsoft.AspNetCore.Mvc.RazorPages": "*******", "Microsoft.AspNetCore.Mvc.TagHelpers": "*******", "Microsoft.AspNetCore.Mvc.ViewFeatures": "*******", "Microsoft.AspNetCore.Razor": "*******", "Microsoft.AspNetCore.Razor.Runtime": "*******", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "*******", "Microsoft.AspNetCore.ResponseCaching": "*******", "Microsoft.AspNetCore.ResponseCompression": "*******", "Microsoft.AspNetCore.Rewrite": "*******", "Microsoft.AspNetCore.Routing.Abstractions": "*******", "Microsoft.AspNetCore.Routing": "*******", "Microsoft.AspNetCore.Server.HttpSys": "*******", "Microsoft.AspNetCore.Server.IIS": "*******", "Microsoft.AspNetCore.Server.IISIntegration": "*******", "Microsoft.AspNetCore.Server.Kestrel.Core": "*******", "Microsoft.AspNetCore.Server.Kestrel": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "*******", "Microsoft.AspNetCore.Session": "*******", "Microsoft.AspNetCore.SignalR.Common": "*******", "Microsoft.AspNetCore.SignalR.Core": "*******", "Microsoft.AspNetCore.SignalR": "*******", "Microsoft.AspNetCore.SignalR.Protocols.Json": "*******", "Microsoft.AspNetCore.StaticFiles": "*******", "Microsoft.AspNetCore.WebSockets": "*******", "Microsoft.AspNetCore.WebUtilities": "*******", "Microsoft.CSharp.Reference": "*******", "Microsoft.Extensions.Caching.Abstractions.Reference": "*******", "Microsoft.Extensions.Caching.Memory.Reference": "*******", "Microsoft.Extensions.Configuration.Abstractions.Reference": "*******", "Microsoft.Extensions.Configuration.Binder.Reference": "*******", "Microsoft.Extensions.Configuration.CommandLine": "*******", "Microsoft.Extensions.Configuration.Reference": "*******", "Microsoft.Extensions.Configuration.EnvironmentVariables": "*******", "Microsoft.Extensions.Configuration.FileExtensions": "*******", "Microsoft.Extensions.Configuration.Ini": "*******", "Microsoft.Extensions.Configuration.Json": "*******", "Microsoft.Extensions.Configuration.KeyPerFile": "*******", "Microsoft.Extensions.Configuration.UserSecrets": "*******", "Microsoft.Extensions.Configuration.Xml": "*******", "Microsoft.Extensions.DependencyInjection.Abstractions.Reference": "*******", "Microsoft.Extensions.DependencyInjection.Reference": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks": "*******", "Microsoft.Extensions.FileProviders.Abstractions": "*******", "Microsoft.Extensions.FileProviders.Composite": "*******", "Microsoft.Extensions.FileProviders.Embedded": "*******", "Microsoft.Extensions.FileProviders.Physical": "*******", "Microsoft.Extensions.FileSystemGlobbing": "*******", "Microsoft.Extensions.Hosting.Abstractions": "*******", "Microsoft.Extensions.Hosting": "*******", "Microsoft.Extensions.Http": "*******", "Microsoft.Extensions.Identity.Core": "*******", "Microsoft.Extensions.Identity.Stores": "*******", "Microsoft.Extensions.Localization.Abstractions": "*******", "Microsoft.Extensions.Localization": "*******", "Microsoft.Extensions.Logging.Abstractions.Reference": "*******", "Microsoft.Extensions.Logging.Configuration": "*******", "Microsoft.Extensions.Logging.Console": "*******", "Microsoft.Extensions.Logging.Debug": "*******", "Microsoft.Extensions.Logging.Reference": "*******", "Microsoft.Extensions.Logging.EventLog": "*******", "Microsoft.Extensions.Logging.EventSource": "*******", "Microsoft.Extensions.Logging.TraceSource": "*******", "Microsoft.Extensions.ObjectPool": "*******", "Microsoft.Extensions.Options.ConfigurationExtensions": "*******", "Microsoft.Extensions.Options.DataAnnotations": "*******", "Microsoft.Extensions.Options.Reference": "*******", "Microsoft.Extensions.Primitives.Reference": "*******", "Microsoft.Extensions.WebEncoders": "*******", "Microsoft.JSInterop": "*******", "Microsoft.Net.Http.Headers": "*******", "Microsoft.VisualBasic.Core": "10.0.5.0", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives.Reference": "*******", "Microsoft.Win32.Registry": "*******", "mscorlib": "*******", "netstandard": "2.1.0.0", "System.AppContext.Reference": "*******", "System.Buffers.Reference": "*******", "System.Collections.Concurrent.Reference": "4.0.15.0", "System.Collections.Reference": "*******", "System.Collections.Immutable.Reference": "1.2.5.0", "System.Collections.NonGeneric": "*******", "System.Collections.Specialized": "*******", "System.ComponentModel.Annotations.Reference": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel": "*******", "System.ComponentModel.EventBasedAsync": "*******", "System.ComponentModel.Primitives": "*******", "System.ComponentModel.TypeConverter": "*******", "System.Configuration": "*******", "System.Console.Reference": "*******", "System.Core": "*******", "System.Data.Common.Reference": "*******", "System.Data.DataSetExtensions": "*******", "System.Data": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug.Reference": "*******", "System.Diagnostics.DiagnosticSource.Reference": "*******", "System.Diagnostics.EventLog": "*******", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools.Reference": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing.Reference": "*******", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "*******", "System.Globalization.Calendars.Reference": "*******", "System.Globalization.Reference": "*******", "System.Globalization.Extensions.Reference": "*******", "System.IO.Compression.Brotli": "*******", "System.IO.Compression.Reference": "*******", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile.Reference": "*******", "System.IO.Reference": "*******", "System.IO.FileSystem.Reference": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives.Reference": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipelines": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq.Reference": "*******", "System.Linq.Expressions.Reference": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable": "*******", "System.Memory": "*******", "System.Net": "*******", "System.Net.Http.Reference": "*******", "System.Net.HttpListener": "*******", "System.Net.Mail": "*******", "System.Net.NameResolution.Reference": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives.Reference": "*******", "System.Net.Requests": "*******", "System.Net.Security": "*******", "System.Net.ServicePoint": "*******", "System.Net.Sockets.Reference": "*******", "System.Net.WebClient": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebProxy": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.Numerics": "*******", "System.Numerics.Vectors": "*******", "System.ObjectModel.Reference": "*******", "System.Reflection.DispatchProxy": "4.0.6.0", "System.Reflection.Reference": "*******", "System.Reflection.Emit.Reference": "*******", "System.Reflection.Emit.ILGeneration.Reference": "*******", "System.Reflection.Emit.Lightweight.Reference": "*******", "System.Reflection.Extensions.Reference": "*******", "System.Reflection.Metadata": "1.4.5.0", "System.Reflection.Primitives.Reference": "*******", "System.Reflection.TypeExtensions.Reference": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager.Reference": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.Unsafe": "4.0.6.0", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime.Reference": "*******", "System.Runtime.Extensions.Reference": "*******", "System.Runtime.Handles.Reference": "*******", "System.Runtime.InteropServices.Reference": "*******", "System.Runtime.InteropServices.RuntimeInformation.Reference": "*******", "System.Runtime.InteropServices.WindowsRuntime": "*******", "System.Runtime.Intrinsics": "*******", "System.Runtime.Loader": "*******", "System.Runtime.Numerics.Reference": "*******", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "4.1.5.0", "System.Security.AccessControl": "*******", "System.Security.Claims.Reference": "*******", "System.Security.Cryptography.Algorithms.Reference": "*******", "System.Security.Cryptography.Cng.Reference": "*******", "System.Security.Cryptography.Csp.Reference": "*******", "System.Security.Cryptography.Encoding.Reference": "*******", "System.Security.Cryptography.Primitives.Reference": "*******", "System.Security.Cryptography.X509Certificates.Reference": "*******", "System.Security.Cryptography.Xml": "*******", "System.Security": "*******", "System.Security.Permissions": "*******", "System.Security.Principal.Reference": "*******", "System.Security.Principal.Windows.Reference": "*******", "System.Security.SecureString": "*******", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages": "*******", "System.Text.Encoding.Reference": "*******", "System.Text.Encoding.Extensions.Reference": "*******", "System.Text.Encodings.Web": "*******", "System.Text.Json": "*******", "System.Text.RegularExpressions.Reference": "*******", "System.Threading.Channels": "*******", "System.Threading.Reference": "*******", "System.Threading.Overlapped": "*******", "System.Threading.Tasks.Dataflow": "*******", "System.Threading.Tasks.Reference": "*******", "System.Threading.Tasks.Extensions.Reference": "*******", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool.Reference": "*******", "System.Threading.Timer.Reference": "*******", "System.Transactions": "*******", "System.Transactions.Local": "*******", "System.ValueTuple": "*******", "System.Web": "*******", "System.Web.HttpUtility": "*******", "System.Windows": "*******", "System.Windows.Extensions": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter.Reference": "*******", "System.Xml.Serialization": "*******", "System.Xml.XDocument.Reference": "*******", "System.Xml.XmlDocument": "*******", "System.Xml.XmlSerializer": "*******", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******", "WindowsBase": "*******"}, "compile": {"HuzurAksesuar.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/*******": {"compile": {"Microsoft.AspNetCore.Antiforgery.dll": {}}}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Cookies.dll": {}}}, "Microsoft.AspNetCore.Authentication.Core/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Core.dll": {}}}, "Microsoft.AspNetCore.Authentication/*******": {"compile": {"Microsoft.AspNetCore.Authentication.dll": {}}}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"compile": {"Microsoft.AspNetCore.Authentication.OAuth.dll": {}}}, "Microsoft.AspNetCore.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Authorization.dll": {}}}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"compile": {"Microsoft.AspNetCore.Authorization.Policy.dll": {}}}, "Microsoft.AspNetCore.Components.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Components.Authorization.dll": {}}}, "Microsoft.AspNetCore.Components/*******": {"compile": {"Microsoft.AspNetCore.Components.dll": {}}}, "Microsoft.AspNetCore.Components.Forms/*******": {"compile": {"Microsoft.AspNetCore.Components.Forms.dll": {}}}, "Microsoft.AspNetCore.Components.Server/*******": {"compile": {"Microsoft.AspNetCore.Components.Server.dll": {}}}, "Microsoft.AspNetCore.Components.Web/*******": {"compile": {"Microsoft.AspNetCore.Components.Web.dll": {}}}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Connections.Abstractions.dll": {}}}, "Microsoft.AspNetCore.CookiePolicy/*******": {"compile": {"Microsoft.AspNetCore.CookiePolicy.dll": {}}}, "Microsoft.AspNetCore.Cors/*******": {"compile": {"Microsoft.AspNetCore.Cors.dll": {}}}, "Microsoft.AspNetCore.Cryptography.Internal/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.Internal.dll": {}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}}, "Microsoft.AspNetCore.DataProtection/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.dll": {}}}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Diagnostics/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.dll": {}}}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}}, "Microsoft.AspNetCore/*******": {"compile": {"Microsoft.AspNetCore.dll": {}}}, "Microsoft.AspNetCore.HostFiltering/*******": {"compile": {"Microsoft.AspNetCore.HostFiltering.dll": {}}}, "Microsoft.AspNetCore.Hosting.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Hosting/*******": {"compile": {"Microsoft.AspNetCore.Hosting.dll": {}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Html.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Http.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.Common.dll": {}}}, "Microsoft.AspNetCore.Http.Connections/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.dll": {}}}, "Microsoft.AspNetCore.Http/*******": {"compile": {"Microsoft.AspNetCore.Http.dll": {}}}, "Microsoft.AspNetCore.Http.Extensions/*******": {"compile": {"Microsoft.AspNetCore.Http.Extensions.dll": {}}}, "Microsoft.AspNetCore.Http.Features/*******": {"compile": {"Microsoft.AspNetCore.Http.Features.dll": {}}}, "Microsoft.AspNetCore.HttpOverrides/*******": {"compile": {"Microsoft.AspNetCore.HttpOverrides.dll": {}}}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"compile": {"Microsoft.AspNetCore.HttpsPolicy.dll": {}}}, "Microsoft.AspNetCore.Identity/*******": {"compile": {"Microsoft.AspNetCore.Identity.dll": {}}}, "Microsoft.AspNetCore.Localization/*******": {"compile": {"Microsoft.AspNetCore.Localization.dll": {}}}, "Microsoft.AspNetCore.Localization.Routing/*******": {"compile": {"Microsoft.AspNetCore.Localization.Routing.dll": {}}}, "Microsoft.AspNetCore.Metadata/*******": {"compile": {"Microsoft.AspNetCore.Metadata.dll": {}}}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}}, "Microsoft.AspNetCore.Mvc.Core/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Core.dll": {}}}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Cors.dll": {}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"compile": {"Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}}, "Microsoft.AspNetCore.Mvc/*******": {"compile": {"Microsoft.AspNetCore.Mvc.dll": {}}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Localization.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Razor.dll": {}}}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"compile": {"Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"compile": {"Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}}, "Microsoft.AspNetCore.Razor/*******": {"compile": {"Microsoft.AspNetCore.Razor.dll": {}}}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"compile": {"Microsoft.AspNetCore.Razor.Runtime.dll": {}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}}, "Microsoft.AspNetCore.ResponseCaching/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.dll": {}}}, "Microsoft.AspNetCore.ResponseCompression/*******": {"compile": {"Microsoft.AspNetCore.ResponseCompression.dll": {}}}, "Microsoft.AspNetCore.Rewrite/*******": {"compile": {"Microsoft.AspNetCore.Rewrite.dll": {}}}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Routing.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Routing/*******": {"compile": {"Microsoft.AspNetCore.Routing.dll": {}}}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"compile": {"Microsoft.AspNetCore.Server.HttpSys.dll": {}}}, "Microsoft.AspNetCore.Server.IIS/*******": {"compile": {"Microsoft.AspNetCore.Server.IIS.dll": {}}}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"compile": {"Microsoft.AspNetCore.Server.IISIntegration.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}}, "Microsoft.AspNetCore.Session/*******": {"compile": {"Microsoft.AspNetCore.Session.dll": {}}}, "Microsoft.AspNetCore.SignalR.Common/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Common.dll": {}}}, "Microsoft.AspNetCore.SignalR.Core/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Core.dll": {}}}, "Microsoft.AspNetCore.SignalR/*******": {"compile": {"Microsoft.AspNetCore.SignalR.dll": {}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}}, "Microsoft.AspNetCore.StaticFiles/*******": {"compile": {"Microsoft.AspNetCore.StaticFiles.dll": {}}}, "Microsoft.AspNetCore.WebSockets/*******": {"compile": {"Microsoft.AspNetCore.WebSockets.dll": {}}}, "Microsoft.AspNetCore.WebUtilities/*******": {"compile": {"Microsoft.AspNetCore.WebUtilities.dll": {}}}, "Microsoft.CSharp.Reference/*******": {"compile": {"Microsoft.CSharp.dll": {}}}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Abstractions.dll": {}}}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Memory.dll": {}}}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Binder.dll": {}}}, "Microsoft.Extensions.Configuration.CommandLine/*******": {"compile": {"Microsoft.Extensions.Configuration.CommandLine.dll": {}}}, "Microsoft.Extensions.Configuration.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.dll": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/*******": {"compile": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/*******": {"compile": {"Microsoft.Extensions.Configuration.FileExtensions.dll": {}}}, "Microsoft.Extensions.Configuration.Ini/*******": {"compile": {"Microsoft.Extensions.Configuration.Ini.dll": {}}}, "Microsoft.Extensions.Configuration.Json/*******": {"compile": {"Microsoft.Extensions.Configuration.Json.dll": {}}}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"compile": {"Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/*******": {"compile": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {}}}, "Microsoft.Extensions.Configuration.Xml/*******": {"compile": {"Microsoft.Extensions.Configuration.Xml.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/*******": {"compile": {"Microsoft.Extensions.FileProviders.Abstractions.dll": {}}}, "Microsoft.Extensions.FileProviders.Composite/*******": {"compile": {"Microsoft.Extensions.FileProviders.Composite.dll": {}}}, "Microsoft.Extensions.FileProviders.Embedded/*******": {"compile": {"Microsoft.Extensions.FileProviders.Embedded.dll": {}}}, "Microsoft.Extensions.FileProviders.Physical/*******": {"compile": {"Microsoft.Extensions.FileProviders.Physical.dll": {}}}, "Microsoft.Extensions.FileSystemGlobbing/*******": {"compile": {"Microsoft.Extensions.FileSystemGlobbing.dll": {}}}, "Microsoft.Extensions.Hosting.Abstractions/*******": {"compile": {"Microsoft.Extensions.Hosting.Abstractions.dll": {}}}, "Microsoft.Extensions.Hosting/*******": {"compile": {"Microsoft.Extensions.Hosting.dll": {}}}, "Microsoft.Extensions.Http/*******": {"compile": {"Microsoft.Extensions.Http.dll": {}}}, "Microsoft.Extensions.Identity.Core/*******": {"compile": {"Microsoft.Extensions.Identity.Core.dll": {}}}, "Microsoft.Extensions.Identity.Stores/*******": {"compile": {"Microsoft.Extensions.Identity.Stores.dll": {}}}, "Microsoft.Extensions.Localization.Abstractions/*******": {"compile": {"Microsoft.Extensions.Localization.Abstractions.dll": {}}}, "Microsoft.Extensions.Localization/*******": {"compile": {"Microsoft.Extensions.Localization.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging.Configuration/*******": {"compile": {"Microsoft.Extensions.Logging.Configuration.dll": {}}}, "Microsoft.Extensions.Logging.Console/*******": {"compile": {"Microsoft.Extensions.Logging.Console.dll": {}}}, "Microsoft.Extensions.Logging.Debug/*******": {"compile": {"Microsoft.Extensions.Logging.Debug.dll": {}}}, "Microsoft.Extensions.Logging.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.dll": {}}}, "Microsoft.Extensions.Logging.EventLog/*******": {"compile": {"Microsoft.Extensions.Logging.EventLog.dll": {}}}, "Microsoft.Extensions.Logging.EventSource/*******": {"compile": {"Microsoft.Extensions.Logging.EventSource.dll": {}}}, "Microsoft.Extensions.Logging.TraceSource/*******": {"compile": {"Microsoft.Extensions.Logging.TraceSource.dll": {}}}, "Microsoft.Extensions.ObjectPool/*******": {"compile": {"Microsoft.Extensions.ObjectPool.dll": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/*******": {"compile": {"Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"compile": {"Microsoft.Extensions.Options.DataAnnotations.dll": {}}}, "Microsoft.Extensions.Options.Reference/*******": {"compile": {"Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Primitives.Reference/*******": {"compile": {"Microsoft.Extensions.Primitives.dll": {}}}, "Microsoft.Extensions.WebEncoders/*******": {"compile": {"Microsoft.Extensions.WebEncoders.dll": {}}}, "Microsoft.JSInterop/*******": {"compile": {"Microsoft.JSInterop.dll": {}}}, "Microsoft.Net.Http.Headers/*******": {"compile": {"Microsoft.Net.Http.Headers.dll": {}}}, "Microsoft.VisualBasic.Core/10.0.5.0": {"compile": {"Microsoft.VisualBasic.Core.dll": {}}}, "Microsoft.VisualBasic/10.0.0.0": {"compile": {"Microsoft.VisualBasic.dll": {}}}, "Microsoft.Win32.Primitives.Reference/*******": {"compile": {"Microsoft.Win32.Primitives.dll": {}}}, "Microsoft.Win32.Registry/*******": {"compile": {"Microsoft.Win32.Registry.dll": {}}}, "mscorlib/*******": {"compile": {"mscorlib.dll": {}}}, "netstandard/2.1.0.0": {"compile": {"netstandard.dll": {}}}, "System.AppContext.Reference/*******": {"compile": {"System.AppContext.dll": {}}}, "System.Buffers.Reference/*******": {"compile": {"System.Buffers.dll": {}}}, "System.Collections.Concurrent.Reference/4.0.15.0": {"compile": {"System.Collections.Concurrent.dll": {}}}, "System.Collections.Reference/*******": {"compile": {"System.Collections.dll": {}}}, "System.Collections.Immutable.Reference/1.2.5.0": {"compile": {"System.Collections.Immutable.dll": {}}}, "System.Collections.NonGeneric/*******": {"compile": {"System.Collections.NonGeneric.dll": {}}}, "System.Collections.Specialized/*******": {"compile": {"System.Collections.Specialized.dll": {}}}, "System.ComponentModel.Annotations.Reference/*******": {"compile": {"System.ComponentModel.Annotations.dll": {}}}, "System.ComponentModel.DataAnnotations/*******": {"compile": {"System.ComponentModel.DataAnnotations.dll": {}}}, "System.ComponentModel/*******": {"compile": {"System.ComponentModel.dll": {}}}, "System.ComponentModel.EventBasedAsync/*******": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}}, "System.ComponentModel.Primitives/*******": {"compile": {"System.ComponentModel.Primitives.dll": {}}}, "System.ComponentModel.TypeConverter/*******": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}}, "System.Configuration/*******": {"compile": {"System.Configuration.dll": {}}}, "System.Console.Reference/*******": {"compile": {"System.Console.dll": {}}}, "System.Core/*******": {"compile": {"System.Core.dll": {}}}, "System.Data.Common.Reference/*******": {"compile": {"System.Data.Common.dll": {}}}, "System.Data.DataSetExtensions/*******": {"compile": {"System.Data.DataSetExtensions.dll": {}}}, "System.Data/*******": {"compile": {"System.Data.dll": {}}}, "System.Diagnostics.Contracts/*******": {"compile": {"System.Diagnostics.Contracts.dll": {}}}, "System.Diagnostics.Debug.Reference/*******": {"compile": {"System.Diagnostics.Debug.dll": {}}}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"compile": {"System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Diagnostics.EventLog/*******": {"compile": {"System.Diagnostics.EventLog.dll": {}}}, "System.Diagnostics.FileVersionInfo/*******": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}}, "System.Diagnostics.Process/*******": {"compile": {"System.Diagnostics.Process.dll": {}}}, "System.Diagnostics.StackTrace/*******": {"compile": {"System.Diagnostics.StackTrace.dll": {}}}, "System.Diagnostics.TextWriterTraceListener/*******": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}}, "System.Diagnostics.Tools.Reference/*******": {"compile": {"System.Diagnostics.Tools.dll": {}}}, "System.Diagnostics.TraceSource/*******": {"compile": {"System.Diagnostics.TraceSource.dll": {}}}, "System.Diagnostics.Tracing.Reference/*******": {"compile": {"System.Diagnostics.Tracing.dll": {}}}, "System/*******": {"compile": {"System.dll": {}}}, "System.Drawing/*******": {"compile": {"System.Drawing.dll": {}}}, "System.Drawing.Primitives/*******": {"compile": {"System.Drawing.Primitives.dll": {}}}, "System.Dynamic.Runtime/*******": {"compile": {"System.Dynamic.Runtime.dll": {}}}, "System.Globalization.Calendars.Reference/*******": {"compile": {"System.Globalization.Calendars.dll": {}}}, "System.Globalization.Reference/*******": {"compile": {"System.Globalization.dll": {}}}, "System.Globalization.Extensions.Reference/*******": {"compile": {"System.Globalization.Extensions.dll": {}}}, "System.IO.Compression.Brotli/*******": {"compile": {"System.IO.Compression.Brotli.dll": {}}}, "System.IO.Compression.Reference/*******": {"compile": {"System.IO.Compression.dll": {}}}, "System.IO.Compression.FileSystem/*******": {"compile": {"System.IO.Compression.FileSystem.dll": {}}}, "System.IO.Compression.ZipFile.Reference/*******": {"compile": {"System.IO.Compression.ZipFile.dll": {}}}, "System.IO.Reference/*******": {"compile": {"System.IO.dll": {}}}, "System.IO.FileSystem.Reference/*******": {"compile": {"System.IO.FileSystem.dll": {}}}, "System.IO.FileSystem.DriveInfo/*******": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}}, "System.IO.FileSystem.Primitives.Reference/*******": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}}, "System.IO.FileSystem.Watcher/*******": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}}, "System.IO.IsolatedStorage/*******": {"compile": {"System.IO.IsolatedStorage.dll": {}}}, "System.IO.MemoryMappedFiles/*******": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}}, "System.IO.Pipelines/*******": {"compile": {"System.IO.Pipelines.dll": {}}}, "System.IO.Pipes/*******": {"compile": {"System.IO.Pipes.dll": {}}}, "System.IO.UnmanagedMemoryStream/*******": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}}, "System.Linq.Reference/*******": {"compile": {"System.Linq.dll": {}}}, "System.Linq.Expressions.Reference/*******": {"compile": {"System.Linq.Expressions.dll": {}}}, "System.Linq.Parallel/*******": {"compile": {"System.Linq.Parallel.dll": {}}}, "System.Linq.Queryable/*******": {"compile": {"System.Linq.Queryable.dll": {}}}, "System.Memory/*******": {"compile": {"System.Memory.dll": {}}}, "System.Net/*******": {"compile": {"System.Net.dll": {}}}, "System.Net.Http.Reference/*******": {"compile": {"System.Net.Http.dll": {}}}, "System.Net.HttpListener/*******": {"compile": {"System.Net.HttpListener.dll": {}}}, "System.Net.Mail/*******": {"compile": {"System.Net.Mail.dll": {}}}, "System.Net.NameResolution.Reference/*******": {"compile": {"System.Net.NameResolution.dll": {}}}, "System.Net.NetworkInformation/*******": {"compile": {"System.Net.NetworkInformation.dll": {}}}, "System.Net.Ping/*******": {"compile": {"System.Net.Ping.dll": {}}}, "System.Net.Primitives.Reference/*******": {"compile": {"System.Net.Primitives.dll": {}}}, "System.Net.Requests/*******": {"compile": {"System.Net.Requests.dll": {}}}, "System.Net.Security/*******": {"compile": {"System.Net.Security.dll": {}}}, "System.Net.ServicePoint/*******": {"compile": {"System.Net.ServicePoint.dll": {}}}, "System.Net.Sockets.Reference/*******": {"compile": {"System.Net.Sockets.dll": {}}}, "System.Net.WebClient/*******": {"compile": {"System.Net.WebClient.dll": {}}}, "System.Net.WebHeaderCollection/*******": {"compile": {"System.Net.WebHeaderCollection.dll": {}}}, "System.Net.WebProxy/*******": {"compile": {"System.Net.WebProxy.dll": {}}}, "System.Net.WebSockets.Client/*******": {"compile": {"System.Net.WebSockets.Client.dll": {}}}, "System.Net.WebSockets/*******": {"compile": {"System.Net.WebSockets.dll": {}}}, "System.Numerics/*******": {"compile": {"System.Numerics.dll": {}}}, "System.Numerics.Vectors/*******": {"compile": {"System.Numerics.Vectors.dll": {}}}, "System.ObjectModel.Reference/*******": {"compile": {"System.ObjectModel.dll": {}}}, "System.Reflection.DispatchProxy/4.0.6.0": {"compile": {"System.Reflection.DispatchProxy.dll": {}}}, "System.Reflection.Reference/*******": {"compile": {"System.Reflection.dll": {}}}, "System.Reflection.Emit.Reference/*******": {"compile": {"System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"compile": {"System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight.Reference/*******": {"compile": {"System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions.Reference/*******": {"compile": {"System.Reflection.Extensions.dll": {}}}, "System.Reflection.Metadata/1.4.5.0": {"compile": {"System.Reflection.Metadata.dll": {}}}, "System.Reflection.Primitives.Reference/*******": {"compile": {"System.Reflection.Primitives.dll": {}}}, "System.Reflection.TypeExtensions.Reference/*******": {"compile": {"System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.Reader/*******": {"compile": {"System.Resources.Reader.dll": {}}}, "System.Resources.ResourceManager.Reference/*******": {"compile": {"System.Resources.ResourceManager.dll": {}}}, "System.Resources.Writer/*******": {"compile": {"System.Resources.Writer.dll": {}}}, "System.Runtime.CompilerServices.Unsafe/4.0.6.0": {"compile": {"System.Runtime.CompilerServices.Unsafe.dll": {}}}, "System.Runtime.CompilerServices.VisualC/*******": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}}, "System.Runtime.Reference/*******": {"compile": {"System.Runtime.dll": {}}}, "System.Runtime.Extensions.Reference/*******": {"compile": {"System.Runtime.Extensions.dll": {}}}, "System.Runtime.Handles.Reference/*******": {"compile": {"System.Runtime.Handles.dll": {}}}, "System.Runtime.InteropServices.Reference/*******": {"compile": {"System.Runtime.InteropServices.dll": {}}}, "System.Runtime.InteropServices.RuntimeInformation.Reference/*******": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}}, "System.Runtime.InteropServices.WindowsRuntime/*******": {"compile": {"System.Runtime.InteropServices.WindowsRuntime.dll": {}}}, "System.Runtime.Intrinsics/*******": {"compile": {"System.Runtime.Intrinsics.dll": {}}}, "System.Runtime.Loader/*******": {"compile": {"System.Runtime.Loader.dll": {}}}, "System.Runtime.Numerics.Reference/*******": {"compile": {"System.Runtime.Numerics.dll": {}}}, "System.Runtime.Serialization/*******": {"compile": {"System.Runtime.Serialization.dll": {}}}, "System.Runtime.Serialization.Formatters/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}}, "System.Runtime.Serialization.Json/*******": {"compile": {"System.Runtime.Serialization.Json.dll": {}}}, "System.Runtime.Serialization.Primitives/*******": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}}, "System.Runtime.Serialization.Xml/4.1.5.0": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}}, "System.Security.AccessControl/*******": {"compile": {"System.Security.AccessControl.dll": {}}}, "System.Security.Claims.Reference/*******": {"compile": {"System.Security.Claims.dll": {}}}, "System.Security.Cryptography.Algorithms.Reference/*******": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}}, "System.Security.Cryptography.Cng.Reference/*******": {"compile": {"System.Security.Cryptography.Cng.dll": {}}}, "System.Security.Cryptography.Csp.Reference/*******": {"compile": {"System.Security.Cryptography.Csp.dll": {}}}, "System.Security.Cryptography.Encoding.Reference/*******": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}}, "System.Security.Cryptography.Primitives.Reference/*******": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}}, "System.Security.Cryptography.X509Certificates.Reference/*******": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}}, "System.Security.Cryptography.Xml/*******": {"compile": {"System.Security.Cryptography.Xml.dll": {}}}, "System.Security/*******": {"compile": {"System.Security.dll": {}}}, "System.Security.Permissions/*******": {"compile": {"System.Security.Permissions.dll": {}}}, "System.Security.Principal.Reference/*******": {"compile": {"System.Security.Principal.dll": {}}}, "System.Security.Principal.Windows.Reference/*******": {"compile": {"System.Security.Principal.Windows.dll": {}}}, "System.Security.SecureString/*******": {"compile": {"System.Security.SecureString.dll": {}}}, "System.ServiceModel.Web/*******": {"compile": {"System.ServiceModel.Web.dll": {}}}, "System.ServiceProcess/*******": {"compile": {"System.ServiceProcess.dll": {}}}, "System.Text.Encoding.CodePages/*******": {"compile": {"System.Text.Encoding.CodePages.dll": {}}}, "System.Text.Encoding.Reference/*******": {"compile": {"System.Text.Encoding.dll": {}}}, "System.Text.Encoding.Extensions.Reference/*******": {"compile": {"System.Text.Encoding.Extensions.dll": {}}}, "System.Text.Encodings.Web/*******": {"compile": {"System.Text.Encodings.Web.dll": {}}}, "System.Text.Json/*******": {"compile": {"System.Text.Json.dll": {}}}, "System.Text.RegularExpressions.Reference/*******": {"compile": {"System.Text.RegularExpressions.dll": {}}}, "System.Threading.Channels/*******": {"compile": {"System.Threading.Channels.dll": {}}}, "System.Threading.Reference/*******": {"compile": {"System.Threading.dll": {}}}, "System.Threading.Overlapped/*******": {"compile": {"System.Threading.Overlapped.dll": {}}}, "System.Threading.Tasks.Dataflow/*******": {"compile": {"System.Threading.Tasks.Dataflow.dll": {}}}, "System.Threading.Tasks.Reference/*******": {"compile": {"System.Threading.Tasks.dll": {}}}, "System.Threading.Tasks.Extensions.Reference/*******": {"compile": {"System.Threading.Tasks.Extensions.dll": {}}}, "System.Threading.Tasks.Parallel/*******": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}}, "System.Threading.Thread/*******": {"compile": {"System.Threading.Thread.dll": {}}}, "System.Threading.ThreadPool.Reference/*******": {"compile": {"System.Threading.ThreadPool.dll": {}}}, "System.Threading.Timer.Reference/*******": {"compile": {"System.Threading.Timer.dll": {}}}, "System.Transactions/*******": {"compile": {"System.Transactions.dll": {}}}, "System.Transactions.Local/*******": {"compile": {"System.Transactions.Local.dll": {}}}, "System.ValueTuple/*******": {"compile": {"System.ValueTuple.dll": {}}}, "System.Web/*******": {"compile": {"System.Web.dll": {}}}, "System.Web.HttpUtility/*******": {"compile": {"System.Web.HttpUtility.dll": {}}}, "System.Windows/*******": {"compile": {"System.Windows.dll": {}}}, "System.Windows.Extensions/*******": {"compile": {"System.Windows.Extensions.dll": {}}}, "System.Xml/*******": {"compile": {"System.Xml.dll": {}}}, "System.Xml.Linq/*******": {"compile": {"System.Xml.Linq.dll": {}}}, "System.Xml.ReaderWriter.Reference/*******": {"compile": {"System.Xml.ReaderWriter.dll": {}}}, "System.Xml.Serialization/*******": {"compile": {"System.Xml.Serialization.dll": {}}}, "System.Xml.XDocument.Reference/*******": {"compile": {"System.Xml.XDocument.dll": {}}}, "System.Xml.XmlDocument/*******": {"compile": {"System.Xml.XmlDocument.dll": {}}}, "System.Xml.XmlSerializer/*******": {"compile": {"System.Xml.XmlSerializer.dll": {}}}, "System.Xml.XPath/*******": {"compile": {"System.Xml.XPath.dll": {}}}, "System.Xml.XPath.XDocument/*******": {"compile": {"System.Xml.XPath.XDocument.dll": {}}}, "WindowsBase/*******": {"compile": {"WindowsBase.dll": {}}}, "ExcelDataReader/3.1.0": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Data.Common": "4.3.0"}, "compile": {"lib/netstandard1.3/ExcelDataReader.dll": {}}}, "ExcelDataReader.DataSet/3.1.0": {"dependencies": {"ExcelDataReader": "3.1.0"}, "compile": {"lib/net45/ExcelDataReader.DataSet.dll": {}}}, "Microsoft.AspNetCore.JsonPatch/3.1.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "12.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/3.1.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "3.1.0", "Newtonsoft.Json": "12.0.2", "Newtonsoft.Json.Bson": "1.0.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"compile": {"ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.Bcl.HashCode/1.1.0": {"compile": {"ref/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/3.1.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.0", "Microsoft.Bcl.HashCode": "1.1.0", "Microsoft.EntityFrameworkCore.Abstractions": "3.1.0", "Microsoft.EntityFrameworkCore.Analyzers": "3.1.0", "Microsoft.Extensions.Caching.Memory": "3.1.0", "Microsoft.Extensions.DependencyInjection": "3.1.0", "Microsoft.Extensions.Logging": "3.1.0", "System.Collections.Immutable": "1.7.0", "System.ComponentModel.Annotations": "4.7.0", "System.Diagnostics.DiagnosticSource": "4.7.0"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/3.1.0": {"compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}}, "Microsoft.EntityFrameworkCore.Analyzers/3.1.0": {}, "Microsoft.EntityFrameworkCore.Relational/3.1.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "3.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {}}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/3.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.0"}}, "Microsoft.Extensions.Caching.Memory/3.1.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "3.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.0", "Microsoft.Extensions.Options": "3.1.0"}}, "Microsoft.Extensions.Configuration/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.0"}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.0"}}, "Microsoft.Extensions.Configuration.Binder/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.0"}}, "Microsoft.Extensions.DependencyInjection/3.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.0": {}, "Microsoft.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "3.1.0", "Microsoft.Extensions.DependencyInjection": "3.1.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.0", "Microsoft.Extensions.Options": "3.1.0"}}, "Microsoft.Extensions.Logging.Abstractions/3.1.0": {}, "Microsoft.Extensions.Options/3.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.0", "Microsoft.Extensions.Primitives": "3.1.0"}}, "Microsoft.Extensions.Primitives/3.1.0": {}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.2.3": {"compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.unix.Microsoft.Win32.Primitives": "4.3.0"}}, "MySqlConnector/0.61.0": {"compile": {"lib/netcoreapp3.0/MySqlConnector.dll": {}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/12.0.2": {"compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "12.0.2"}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {}}}, "Pomelo.EntityFrameworkCore.MySql/3.1.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "3.1.0", "MySqlConnector": "0.61.0", "Pomelo.JsonObject": "2.2.1"}, "compile": {"lib/netstandard2.0/Pomelo.EntityFrameworkCore.MySql.dll": {}}}, "Pomelo.JsonObject/2.2.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "12.0.2"}, "compile": {"lib/netstandard2.0/Pomelo.JsonObject.dll": {}}}, "runtime.any.System.Collections/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "runtime.any.System.Diagnostics.Tools/4.3.0": {}, "runtime.any.System.Diagnostics.Tracing/4.3.0": {}, "runtime.any.System.Globalization/4.3.0": {}, "runtime.any.System.Globalization.Calendars/4.3.0": {}, "runtime.any.System.IO/4.3.0": {}, "runtime.any.System.Reflection/4.3.0": {}, "runtime.any.System.Reflection.Extensions/4.3.0": {}, "runtime.any.System.Reflection.Primitives/4.3.0": {}, "runtime.any.System.Resources.ResourceManager/4.3.0": {}, "runtime.any.System.Runtime/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "runtime.any.System.Runtime.Handles/4.3.0": {}, "runtime.any.System.Runtime.InteropServices/4.3.0": {}, "runtime.any.System.Text.Encoding/4.3.0": {}, "runtime.any.System.Text.Encoding.Extensions/4.3.0": {}, "runtime.any.System.Threading.Tasks/4.3.0": {}, "runtime.any.System.Threading.Timer/4.3.0": {}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.unix.Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "runtime.native.System": "4.3.0"}}, "runtime.unix.System.Console/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "runtime.unix.System.Diagnostics.Debug/4.3.0": {"dependencies": {"runtime.native.System": "4.3.0"}}, "runtime.unix.System.IO.FileSystem/4.3.0": {"dependencies": {"System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "runtime.unix.System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "runtime.unix.System.Net.Sockets/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.NameResolution": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}}, "runtime.unix.System.Private.Uri/4.3.0": {"dependencies": {"runtime.native.System": "4.3.0"}}, "runtime.unix.System.Runtime.Extensions/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "Swashbuckle.AspNetCore/5.6.3": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "5.6.3", "Swashbuckle.AspNetCore.SwaggerGen": "5.6.3", "Swashbuckle.AspNetCore.SwaggerUI": "5.6.3"}}, "Swashbuckle.AspNetCore.Swagger/5.6.3": {"dependencies": {"Microsoft.OpenApi": "1.2.3"}, "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/5.6.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "5.6.3"}, "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/5.6.3": {"compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Collections": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/1.7.0": {}, "System.ComponentModel.Annotations/4.7.0": {}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.unix.System.Console": "4.3.0"}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.unix.System.Diagnostics.Debug": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/4.7.0": {}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Diagnostics.Tools": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Diagnostics.Tracing": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization.Calendars": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.unix.System.IO.FileSystem": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.7.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.NameResolution/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "runtime.unix.System.Net.Primitives": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.unix.System.Net.Sockets": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.Uri/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.unix.System.Private.Uri": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Extensions": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Resources.ResourceManager": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.unix.System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "runtime.any.System.Runtime.InteropServices": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Security.Principal.Windows/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Principal": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.any.System.Text.Encoding.Extensions": "4.3.0"}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Timer": "4.3.0"}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}}, ".NETCoreApp,Version=v3.1/linux-x64": {"HuzurAksesuar/1.0.0": {"dependencies": {"ExcelDataReader": "3.1.0", "ExcelDataReader.DataSet": "3.1.0", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "3.1.0", "Pomelo.EntityFrameworkCore.MySql": "3.1.0", "Swashbuckle.AspNetCore": "5.6.3", "Microsoft.AspNetCore.Antiforgery": "*******", "Microsoft.AspNetCore.Authentication.Abstractions": "*******", "Microsoft.AspNetCore.Authentication.Cookies": "*******", "Microsoft.AspNetCore.Authentication.Core": "*******", "Microsoft.AspNetCore.Authentication": "*******", "Microsoft.AspNetCore.Authentication.OAuth": "*******", "Microsoft.AspNetCore.Authorization": "*******", "Microsoft.AspNetCore.Authorization.Policy": "*******", "Microsoft.AspNetCore.Components.Authorization": "*******", "Microsoft.AspNetCore.Components": "*******", "Microsoft.AspNetCore.Components.Forms": "*******", "Microsoft.AspNetCore.Components.Server": "*******", "Microsoft.AspNetCore.Components.Web": "*******", "Microsoft.AspNetCore.Connections.Abstractions": "*******", "Microsoft.AspNetCore.CookiePolicy": "*******", "Microsoft.AspNetCore.Cors": "*******", "Microsoft.AspNetCore.Cryptography.Internal": "*******", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "*******", "Microsoft.AspNetCore.DataProtection.Abstractions": "*******", "Microsoft.AspNetCore.DataProtection": "*******", "Microsoft.AspNetCore.DataProtection.Extensions": "*******", "Microsoft.AspNetCore.Diagnostics.Abstractions": "*******", "Microsoft.AspNetCore.Diagnostics": "*******", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "*******", "Microsoft.AspNetCore": "*******", "Microsoft.AspNetCore.HostFiltering": "*******", "Microsoft.AspNetCore.Hosting.Abstractions": "*******", "Microsoft.AspNetCore.Hosting": "*******", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "*******", "Microsoft.AspNetCore.Html.Abstractions": "*******", "Microsoft.AspNetCore.Http.Abstractions": "*******", "Microsoft.AspNetCore.Http.Connections.Common": "*******", "Microsoft.AspNetCore.Http.Connections": "*******", "Microsoft.AspNetCore.Http": "*******", "Microsoft.AspNetCore.Http.Extensions": "*******", "Microsoft.AspNetCore.Http.Features": "*******", "Microsoft.AspNetCore.HttpOverrides": "*******", "Microsoft.AspNetCore.HttpsPolicy": "*******", "Microsoft.AspNetCore.Identity": "*******", "Microsoft.AspNetCore.Localization": "*******", "Microsoft.AspNetCore.Localization.Routing": "*******", "Microsoft.AspNetCore.Metadata": "*******", "Microsoft.AspNetCore.Mvc.Abstractions": "*******", "Microsoft.AspNetCore.Mvc.ApiExplorer": "*******", "Microsoft.AspNetCore.Mvc.Core": "*******", "Microsoft.AspNetCore.Mvc.Cors": "*******", "Microsoft.AspNetCore.Mvc.DataAnnotations": "*******", "Microsoft.AspNetCore.Mvc": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Json": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "*******", "Microsoft.AspNetCore.Mvc.Localization": "*******", "Microsoft.AspNetCore.Mvc.Razor": "*******", "Microsoft.AspNetCore.Mvc.RazorPages": "*******", "Microsoft.AspNetCore.Mvc.TagHelpers": "*******", "Microsoft.AspNetCore.Mvc.ViewFeatures": "*******", "Microsoft.AspNetCore.Razor": "*******", "Microsoft.AspNetCore.Razor.Runtime": "*******", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "*******", "Microsoft.AspNetCore.ResponseCaching": "*******", "Microsoft.AspNetCore.ResponseCompression": "*******", "Microsoft.AspNetCore.Rewrite": "*******", "Microsoft.AspNetCore.Routing.Abstractions": "*******", "Microsoft.AspNetCore.Routing": "*******", "Microsoft.AspNetCore.Server.HttpSys": "*******", "Microsoft.AspNetCore.Server.IIS": "*******", "Microsoft.AspNetCore.Server.IISIntegration": "*******", "Microsoft.AspNetCore.Server.Kestrel.Core": "*******", "Microsoft.AspNetCore.Server.Kestrel": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "*******", "Microsoft.AspNetCore.Session": "*******", "Microsoft.AspNetCore.SignalR.Common": "*******", "Microsoft.AspNetCore.SignalR.Core": "*******", "Microsoft.AspNetCore.SignalR": "*******", "Microsoft.AspNetCore.SignalR.Protocols.Json": "*******", "Microsoft.AspNetCore.StaticFiles": "*******", "Microsoft.AspNetCore.WebSockets": "*******", "Microsoft.AspNetCore.WebUtilities": "*******", "Microsoft.CSharp.Reference": "*******", "Microsoft.Extensions.Caching.Abstractions.Reference": "*******", "Microsoft.Extensions.Caching.Memory.Reference": "*******", "Microsoft.Extensions.Configuration.Abstractions.Reference": "*******", "Microsoft.Extensions.Configuration.Binder.Reference": "*******", "Microsoft.Extensions.Configuration.CommandLine": "*******", "Microsoft.Extensions.Configuration.Reference": "*******", "Microsoft.Extensions.Configuration.EnvironmentVariables": "*******", "Microsoft.Extensions.Configuration.FileExtensions": "*******", "Microsoft.Extensions.Configuration.Ini": "*******", "Microsoft.Extensions.Configuration.Json": "*******", "Microsoft.Extensions.Configuration.KeyPerFile": "*******", "Microsoft.Extensions.Configuration.UserSecrets": "*******", "Microsoft.Extensions.Configuration.Xml": "*******", "Microsoft.Extensions.DependencyInjection.Abstractions.Reference": "*******", "Microsoft.Extensions.DependencyInjection.Reference": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks": "*******", "Microsoft.Extensions.FileProviders.Abstractions": "*******", "Microsoft.Extensions.FileProviders.Composite": "*******", "Microsoft.Extensions.FileProviders.Embedded": "*******", "Microsoft.Extensions.FileProviders.Physical": "*******", "Microsoft.Extensions.FileSystemGlobbing": "*******", "Microsoft.Extensions.Hosting.Abstractions": "*******", "Microsoft.Extensions.Hosting": "*******", "Microsoft.Extensions.Http": "*******", "Microsoft.Extensions.Identity.Core": "*******", "Microsoft.Extensions.Identity.Stores": "*******", "Microsoft.Extensions.Localization.Abstractions": "*******", "Microsoft.Extensions.Localization": "*******", "Microsoft.Extensions.Logging.Abstractions.Reference": "*******", "Microsoft.Extensions.Logging.Configuration": "*******", "Microsoft.Extensions.Logging.Console": "*******", "Microsoft.Extensions.Logging.Debug": "*******", "Microsoft.Extensions.Logging.Reference": "*******", "Microsoft.Extensions.Logging.EventLog": "*******", "Microsoft.Extensions.Logging.EventSource": "*******", "Microsoft.Extensions.Logging.TraceSource": "*******", "Microsoft.Extensions.ObjectPool": "*******", "Microsoft.Extensions.Options.ConfigurationExtensions": "*******", "Microsoft.Extensions.Options.DataAnnotations": "*******", "Microsoft.Extensions.Options.Reference": "*******", "Microsoft.Extensions.Primitives.Reference": "*******", "Microsoft.Extensions.WebEncoders": "*******", "Microsoft.JSInterop": "*******", "Microsoft.Net.Http.Headers": "*******", "Microsoft.VisualBasic.Core": "10.0.5.0", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives.Reference": "*******", "Microsoft.Win32.Registry": "*******", "mscorlib": "*******", "netstandard": "2.1.0.0", "System.AppContext.Reference": "*******", "System.Buffers.Reference": "*******", "System.Collections.Concurrent.Reference": "4.0.15.0", "System.Collections.Reference": "*******", "System.Collections.Immutable.Reference": "1.2.5.0", "System.Collections.NonGeneric": "*******", "System.Collections.Specialized": "*******", "System.ComponentModel.Annotations.Reference": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel": "*******", "System.ComponentModel.EventBasedAsync": "*******", "System.ComponentModel.Primitives": "*******", "System.ComponentModel.TypeConverter": "*******", "System.Configuration": "*******", "System.Console.Reference": "*******", "System.Core": "*******", "System.Data.Common.Reference": "*******", "System.Data.DataSetExtensions": "*******", "System.Data": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug.Reference": "*******", "System.Diagnostics.DiagnosticSource.Reference": "*******", "System.Diagnostics.EventLog": "*******", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools.Reference": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing.Reference": "*******", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "*******", "System.Globalization.Calendars.Reference": "*******", "System.Globalization.Reference": "*******", "System.Globalization.Extensions.Reference": "*******", "System.IO.Compression.Brotli": "*******", "System.IO.Compression.Reference": "*******", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile.Reference": "*******", "System.IO.Reference": "*******", "System.IO.FileSystem.Reference": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives.Reference": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipelines": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq.Reference": "*******", "System.Linq.Expressions.Reference": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable": "*******", "System.Memory": "*******", "System.Net": "*******", "System.Net.Http.Reference": "*******", "System.Net.HttpListener": "*******", "System.Net.Mail": "*******", "System.Net.NameResolution.Reference": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives.Reference": "*******", "System.Net.Requests": "*******", "System.Net.Security": "*******", "System.Net.ServicePoint": "*******", "System.Net.Sockets.Reference": "*******", "System.Net.WebClient": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebProxy": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.Numerics": "*******", "System.Numerics.Vectors": "*******", "System.ObjectModel.Reference": "*******", "System.Reflection.DispatchProxy": "4.0.6.0", "System.Reflection.Reference": "*******", "System.Reflection.Emit.Reference": "*******", "System.Reflection.Emit.ILGeneration.Reference": "*******", "System.Reflection.Emit.Lightweight.Reference": "*******", "System.Reflection.Extensions.Reference": "*******", "System.Reflection.Metadata": "1.4.5.0", "System.Reflection.Primitives.Reference": "*******", "System.Reflection.TypeExtensions.Reference": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager.Reference": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.Unsafe": "4.0.6.0", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime.Reference": "*******", "System.Runtime.Extensions.Reference": "*******", "System.Runtime.Handles.Reference": "*******", "System.Runtime.InteropServices.Reference": "*******", "System.Runtime.InteropServices.RuntimeInformation.Reference": "*******", "System.Runtime.InteropServices.WindowsRuntime": "*******", "System.Runtime.Intrinsics": "*******", "System.Runtime.Loader": "*******", "System.Runtime.Numerics.Reference": "*******", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "4.1.5.0", "System.Security.AccessControl": "*******", "System.Security.Claims.Reference": "*******", "System.Security.Cryptography.Algorithms.Reference": "*******", "System.Security.Cryptography.Cng.Reference": "*******", "System.Security.Cryptography.Csp.Reference": "*******", "System.Security.Cryptography.Encoding.Reference": "*******", "System.Security.Cryptography.Primitives.Reference": "*******", "System.Security.Cryptography.X509Certificates.Reference": "*******", "System.Security.Cryptography.Xml": "*******", "System.Security": "*******", "System.Security.Permissions": "*******", "System.Security.Principal.Reference": "*******", "System.Security.Principal.Windows.Reference": "*******", "System.Security.SecureString": "*******", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages": "*******", "System.Text.Encoding.Reference": "*******", "System.Text.Encoding.Extensions.Reference": "*******", "System.Text.Encodings.Web": "*******", "System.Text.Json": "*******", "System.Text.RegularExpressions.Reference": "*******", "System.Threading.Channels": "*******", "System.Threading.Reference": "*******", "System.Threading.Overlapped": "*******", "System.Threading.Tasks.Dataflow": "*******", "System.Threading.Tasks.Reference": "*******", "System.Threading.Tasks.Extensions.Reference": "*******", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool.Reference": "*******", "System.Threading.Timer.Reference": "*******", "System.Transactions": "*******", "System.Transactions.Local": "*******", "System.ValueTuple": "*******", "System.Web": "*******", "System.Web.HttpUtility": "*******", "System.Windows": "*******", "System.Windows.Extensions": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter.Reference": "*******", "System.Xml.Serialization": "*******", "System.Xml.XDocument.Reference": "*******", "System.Xml.XmlDocument": "*******", "System.Xml.XmlSerializer": "*******", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******", "WindowsBase": "*******", "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64": "3.1.32", "runtimepack.Microsoft.AspNetCore.App.Runtime.linux-x64": "3.1.32"}, "runtime": {"HuzurAksesuar.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/3.1.32": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "10.0.5.0", "fileVersion": "4.700.22.56512"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "4.700.22.56512"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "4.0.15.0", "fileVersion": "4.700.22.56512"}, "System.Collections.Immutable.dll": {"assemblyVersion": "1.2.5.0", "fileVersion": "4.700.22.56512"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "4.1.5.0", "fileVersion": "4.700.22.56512"}, "System.Private.Uri.dll": {"assemblyVersion": "4.0.6.0", "fileVersion": "4.700.22.56512"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "4.0.6.0", "fileVersion": "4.700.22.56512"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "1.4.5.0", "fileVersion": "4.700.22.56512"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "4.1.5.0", "fileVersion": "4.700.22.56512"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "4.0.6.0", "fileVersion": "*******"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.InteropServices.WindowsRuntime.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "4.1.5.0", "fileVersion": "4.700.22.56512"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.WindowsRuntime.UI.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Runtime.WindowsRuntime.dll": {"assemblyVersion": "4.0.15.0", "fileVersion": "4.700.22.56512"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.56512"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "4.700.22.56512"}}, "native": {"SOS_README.md": {"fileVersion": "0.0.0.0"}, "System.Globalization.Native.so": {"fileVersion": "0.0.0.0"}, "System.IO.Compression.Native.a": {"fileVersion": "0.0.0.0"}, "System.IO.Compression.Native.so": {"fileVersion": "0.0.0.0"}, "System.Native.a": {"fileVersion": "0.0.0.0"}, "System.Native.so": {"fileVersion": "0.0.0.0"}, "System.Net.Http.Native.a": {"fileVersion": "0.0.0.0"}, "System.Net.Http.Native.so": {"fileVersion": "0.0.0.0"}, "System.Net.Security.Native.a": {"fileVersion": "0.0.0.0"}, "System.Net.Security.Native.so": {"fileVersion": "0.0.0.0"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.55902"}, "System.Security.Cryptography.Native.OpenSsl.a": {"fileVersion": "0.0.0.0"}, "System.Security.Cryptography.Native.OpenSsl.so": {"fileVersion": "0.0.0.0"}, "createdump": {"fileVersion": "0.0.0.0"}, "libclrjit.so": {"fileVersion": "0.0.0.0"}, "libcoreclr.so": {"fileVersion": "0.0.0.0"}, "libcoreclrtraceptprovider.so": {"fileVersion": "0.0.0.0"}, "libdbgshim.so": {"fileVersion": "0.0.0.0"}, "libhostfxr.so": {"fileVersion": "0.0.0.0"}, "libhostpolicy.so": {"fileVersion": "0.0.0.0"}, "libmscordaccore.so": {"fileVersion": "0.0.0.0"}, "libmscordbi.so": {"fileVersion": "0.0.0.0"}}}, "runtimepack.Microsoft.AspNetCore.App.Runtime.linux-x64/3.1.32": {"runtime": {"Microsoft.AspNetCore.Antiforgery.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Authentication.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Authentication.Cookies.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Authentication.Core.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Authentication.OAuth.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Authentication.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Authorization.Policy.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Components.Authorization.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Components.Server.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.CookiePolicy.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Cors.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.DataProtection.Extensions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Diagnostics.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.HostFiltering.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Hosting.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Hosting.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Html.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Http.Connections.Common.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Http.Connections.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Http.Extensions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.HttpOverrides.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.HttpsPolicy.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Identity.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Localization.Routing.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Localization.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.Core.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.Cors.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.Localization.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.Razor.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.RazorPages.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.TagHelpers.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Mvc.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Razor.Runtime.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Razor.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.ResponseCaching.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.ResponseCompression.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Rewrite.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Routing.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Routing.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Server.HttpSys.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Server.IIS.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Server.IISIntegration.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Server.Kestrel.Core.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Server.Kestrel.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.Session.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.SignalR.Core.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.SignalR.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.StaticFiles.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.WebSockets.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Configuration.Ini.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Configuration.KeyPerFile.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Configuration.Xml.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Http.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Localization.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Logging.TraceSource.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Logging.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Options.DataAnnotations.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Options.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Extensions.WebEncoders.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.JSInterop.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}, "Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.21.51508"}, "System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.12208"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.36301"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "ExcelDataReader/3.1.0": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Data.Common": "4.3.0"}, "runtime": {"lib/netstandard1.3/ExcelDataReader.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ExcelDataReader.DataSet/3.1.0": {"dependencies": {"ExcelDataReader": "3.1.0"}, "runtime": {"lib/net45/ExcelDataReader.DataSet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.JsonPatch/3.1.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56601"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/3.1.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "3.1.0", "Newtonsoft.Json": "12.0.2", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56601"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.19.56404"}}}, "Microsoft.Bcl.HashCode/1.1.0": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.19.56404"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/3.1.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.0", "Microsoft.Bcl.HashCode": "1.1.0", "Microsoft.EntityFrameworkCore.Abstractions": "3.1.0", "Microsoft.EntityFrameworkCore.Analyzers": "3.1.0", "Microsoft.Extensions.Caching.Memory": "3.1.0", "Microsoft.Extensions.DependencyInjection": "3.1.0", "Microsoft.Extensions.Logging": "3.1.0", "System.Collections.Immutable": "1.7.0", "System.ComponentModel.Annotations": "4.7.0", "System.Diagnostics.DiagnosticSource": "4.7.0"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56505"}}}, "Microsoft.EntityFrameworkCore.Abstractions/3.1.0": {"runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56505"}}}, "Microsoft.EntityFrameworkCore.Analyzers/3.1.0": {}, "Microsoft.EntityFrameworkCore.Relational/3.1.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "3.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56505"}}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/3.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.0"}}, "Microsoft.Extensions.Caching.Memory/3.1.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "3.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.0", "Microsoft.Extensions.Options": "3.1.0"}}, "Microsoft.Extensions.Configuration/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.0"}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.0"}}, "Microsoft.Extensions.Configuration.Binder/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.0"}}, "Microsoft.Extensions.DependencyInjection/3.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.0": {}, "Microsoft.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "3.1.0", "Microsoft.Extensions.DependencyInjection": "3.1.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.0", "Microsoft.Extensions.Options": "3.1.0"}}, "Microsoft.Extensions.Logging.Abstractions/3.1.0": {}, "Microsoft.Extensions.Options/3.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.0", "Microsoft.Extensions.Primitives": "3.1.0"}}, "Microsoft.Extensions.Primitives/3.1.0": {}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.2.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.2.3.0", "fileVersion": "1.2.3.0"}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.unix.Microsoft.Win32.Primitives": "4.3.0"}}, "MySqlConnector/0.61.0": {"runtime": {"lib/netcoreapp3.0/MySqlConnector.dll": {"assemblyVersion": "0.61.0.0", "fileVersion": "0.61.0.0"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/12.0.2": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "12.0.0.0", "fileVersion": "12.0.2.23222"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.2.22727"}}}, "Pomelo.EntityFrameworkCore.MySql/3.1.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "3.1.0", "MySqlConnector": "0.61.0", "Pomelo.JsonObject": "2.2.1"}, "runtime": {"lib/netstandard2.0/Pomelo.EntityFrameworkCore.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pomelo.JsonObject/2.2.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/Pomelo.JsonObject.dll": {"assemblyVersion": "2.2.1.0", "fileVersion": "2.2.1.0"}}}, "runtime.any.System.Collections/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "runtime.any.System.Diagnostics.Tools/4.3.0": {}, "runtime.any.System.Diagnostics.Tracing/4.3.0": {}, "runtime.any.System.Globalization/4.3.0": {}, "runtime.any.System.Globalization.Calendars/4.3.0": {}, "runtime.any.System.IO/4.3.0": {}, "runtime.any.System.Reflection/4.3.0": {}, "runtime.any.System.Reflection.Extensions/4.3.0": {}, "runtime.any.System.Reflection.Primitives/4.3.0": {}, "runtime.any.System.Resources.ResourceManager/4.3.0": {}, "runtime.any.System.Runtime/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "runtime.any.System.Runtime.Handles/4.3.0": {}, "runtime.any.System.Runtime.InteropServices/4.3.0": {}, "runtime.any.System.Text.Encoding/4.3.0": {}, "runtime.any.System.Text.Encoding.Extensions/4.3.0": {}, "runtime.any.System.Threading.Tasks/4.3.0": {}, "runtime.any.System.Threading.Timer/4.3.0": {}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.unix.Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "runtime.native.System": "4.3.0"}}, "runtime.unix.System.Console/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "runtime.unix.System.Diagnostics.Debug/4.3.0": {"dependencies": {"runtime.native.System": "4.3.0"}}, "runtime.unix.System.IO.FileSystem/4.3.0": {"dependencies": {"System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "runtime.unix.System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "runtime.unix.System.Net.Sockets/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.NameResolution": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}}, "runtime.unix.System.Private.Uri/4.3.0": {"dependencies": {"runtime.native.System": "4.3.0"}}, "runtime.unix.System.Runtime.Extensions/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "Swashbuckle.AspNetCore/5.6.3": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "5.6.3", "Swashbuckle.AspNetCore.SwaggerGen": "5.6.3", "Swashbuckle.AspNetCore.SwaggerUI": "5.6.3"}}, "Swashbuckle.AspNetCore.Swagger/5.6.3": {"dependencies": {"Microsoft.OpenApi": "1.2.3"}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/5.6.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "5.6.3"}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/5.6.3": {"runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Collections": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/1.7.0": {}, "System.ComponentModel.Annotations/4.7.0": {}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.unix.System.Console": "4.3.0"}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.unix.System.Diagnostics.Debug": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/4.7.0": {}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Diagnostics.Tools": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Diagnostics.Tracing": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization.Calendars": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.unix.System.IO.FileSystem": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.7.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.NameResolution/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "runtime.unix.System.Net.Primitives": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.unix.System.Net.Sockets": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.Uri/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.unix.System.Private.Uri": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Extensions": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Resources.ResourceManager": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.unix.System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "runtime.any.System.Runtime.InteropServices": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Security.Principal.Windows/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Principal": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.any.System.Text.Encoding.Extensions": "4.3.0"}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Timer": "4.3.0"}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}}}, "libraries": {"HuzurAksesuar/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/3.1.32": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.AspNetCore.App.Runtime.linux-x64/3.1.32": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "ExcelDataReader/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-VhaLvSaP13+EUrxWzvg73zAjSmGSZINuE1cM+iAiZkPd7baMM22GRqW1c2L8UwJZ8JeLwSkler6YmpDp0FRYWg==", "path": "exceldatareader/3.1.0", "hashPath": "exceldatareader.3.1.0.nupkg.sha512"}, "ExcelDataReader.DataSet/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-03Efh40/qUxETRUGNna98PYAyH0saif+dilI2+BRAJYSwNWVzVSkBZWgdpehY8DHLc2QCWcUfhTKAp4uOb1Ibg==", "path": "exceldatareader.dataset/3.1.0", "hashPath": "exceldatareader.dataset.3.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-EctKEX24sGLS4Brg2hdxZQc3WwP9MKFvk0d1oa8ilyt8q0rgo73G6ptxQvkFmQwaG+SKnkVV31T2UN3nSYhPGA==", "path": "microsoft.aspnetcore.jsonpatch/3.1.0", "hashPath": "microsoft.aspnetcore.jsonpatch.3.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-DL3tgfLLeLT6bd64MiByrvDJn27Z8DNX4KWM1Ss4ge8zitcB8inNMVCpx4w+uVvdPqkVkLgVgPWIBx/cWXYaVQ==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/3.1.0", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.3.1.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Am6l4Vpn3/K32daEqZI+FFr96OlZkgwK2LcT3pZ2zWubR5zTPW3/FkO1Rat9kb7oQOa4rxgl9LJHc5tspCWfg==", "path": "microsoft.bcl.asyncinterfaces/1.1.0", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-J2G1k+u5unBV+aYcwxo94ip16Rkp65pgWFb0R6zwJipzWNMgvqlWeuI7/+R+e8bob66LnSG+llLJ+z8wI94cHg==", "path": "microsoft.bcl.hashcode/1.1.0", "hashPath": "microsoft.bcl.hashcode.1.1.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BG+K/TBDlmkFUviU8lPvmQ3/nDf9e5MKh1il31gLEToV2kgxgkg+JulhtM0xOph6OU1Iyd5A+3c5FyBpI2xh3A==", "path": "microsoft.entityframeworkcore/3.1.0", "hashPath": "microsoft.entityframeworkcore.3.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-6Wn8vQUq04aeXKVf7pu6/hQxqQliSyM5TocAJUBRQpDNAZGuu3nWMx1biEtZqhGXa4UTLTIJCOb8YANToC8ooA==", "path": "microsoft.entityframeworkcore.abstractions/3.1.0", "hashPath": "microsoft.entityframeworkcore.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Shqqf6MFaIkcuR6dMEBzvG0LMJMoKKMx+k14nUax98vv33i3AUUGxKF9VfJt7VsjOA3xu+KOdzCW8YJ2KLoRIA==", "path": "microsoft.entityframeworkcore.analyzers/3.1.0", "hashPath": "microsoft.entityframeworkcore.analyzers.3.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-eeiqVqzXVuwQsUbqXMyXSEUn/EHB9zmDsr5f/+v6uEt0ir7pgItuIi3I7QV4xvM/s0KbFsqGUOrAFPeRHE3plg==", "path": "microsoft.entityframeworkcore.relational/3.1.0", "hashPath": "microsoft.entityframeworkcore.relational.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w==", "path": "microsoft.extensions.apidescription.server/3.0.0", "hashPath": "microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-+R7RE<PERSON>+Pks1/ITjDdvey+QJzIG3tIYOtrv4RT40UVVe2Y1Sa8pIjJy3MzPZbyXVgOFN3JHFz1UZH8kz04aa5A==", "path": "microsoft.extensions.caching.abstractions/3.1.0", "hashPath": "microsoft.extensions.caching.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-SWVYYFN8K4bUEZAwVbcdxjApYE7JtbjPXIqsQt/vKE243u0qeDuS7bA5hKVr+k5lo2R+BpITe6Mvqmkus2xDRQ==", "path": "microsoft.extensions.caching.memory/3.1.0", "hashPath": "microsoft.extensions.caching.memory.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lu41BWNmwhKr6LgyQvcYBOge0pPvmiaK8R5UHXX4//wBhonJyWcT2OK1mqYfEM5G7pTf31fPrpIHOT6sN7EGOA==", "path": "microsoft.extensions.configuration/3.1.0", "hashPath": "microsoft.extensions.configuration.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ESz6bVoDQX7sgWdKHF6G9Pq672T8k+19AFb/txDXwdz7MoqaNQj2/in3agm/3qae9V+WvQZH86LLTNVo0it8vQ==", "path": "microsoft.extensions.configuration.abstractions/3.1.0", "hashPath": "microsoft.extensions.configuration.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-o9eELDBfNkR7sUtYysFZ1Q7BQ1mYt27DMkups/3vu7xgPyOpMD+iAfrBZFzUXT2iw0fmFb8s1gfNBZS+IgjKdQ==", "path": "microsoft.extensions.configuration.binder/3.1.0", "hashPath": "microsoft.extensions.configuration.binder.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-KVkv3aF2MQpmGFRh4xRx2CNbc2sjDFk+lH4ySrjWSOS+XoY1Xc+sJphw3N0iYOpoeCCq8976ceVYDH8sdx2qIQ==", "path": "microsoft.extensions.dependencyinjection/3.1.0", "hashPath": "microsoft.extensions.dependencyinjection.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-44rDtOf1JXXAFpNT2EXMExaDm/4OJ2RXOL9i9lE4bK427nzC7Exphv+beB6IgluyE2GIoo8zezTStMXI7MQ8WA==", "path": "microsoft.extensions.dependencyinjection.abstractions/3.1.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-P+8sKQ8L4ooL79sxxqwFPxGGC3aBrUDLB/dZqhs4J0XjTyrkeeyJQ4D4nzJB6OnAhy78HIIgQ/RbD6upOXLynw==", "path": "microsoft.extensions.logging/3.1.0", "hashPath": "microsoft.extensions.logging.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-jjo4YXRx6MIpv6DiRxJjSpl+sPP0+5VW0clMEdLyIAz44PPwrDTFrd5PZckIxIXl1kKZ2KK6IL2nkt0+ug2MQg==", "path": "microsoft.extensions.logging.abstractions/3.1.0", "hashPath": "microsoft.extensions.logging.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Options/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9b6JHY7TAXrSfZ6EEGf+j8XnqKIiMPErfmaNXhJYSCb+BUW2H4RtzkNJvwLJzwgzqBP0wtTjyA6Uw4BPPdmkMw==", "path": "microsoft.extensions.options/3.1.0", "hashPath": "microsoft.extensions.options.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-LEKAnX7lhUhSoIc2XraCTK3M4IU/LdVUzCe464Sa4+7F4ZJuXHHRzZli2mDbiT4xzAZhgqXbvfnb5+CNDcQFfg==", "path": "microsoft.extensions.primitives/3.1.0", "hashPath": "microsoft.extensions.primitives.3.1.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "path": "microsoft.openapi/1.2.3", "hashPath": "microsoft.openapi.1.2.3.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "MySqlConnector/0.61.0": {"type": "package", "serviceable": true, "sha512": "sha512-E01yibVzCkVOmhNsUb+B4amjQKfKFlGiR1huNd6Q8aTCqBrACo0I3JMKvJBMpZfYhAsR8bqB9496PwcGpr+LuQ==", "path": "mysqlconnector/0.61.0", "hashPath": "mysqlconnector.0.61.0.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/12.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-rTK0s2EKlfHsQsH6Yx2smvcTCeyoDNgCW7FEYyV01drPlh2T243PR2DiDXqtC5N4GDm4Ma/lkxfW5a/4793vbA==", "path": "newtonsoft.json/12.0.2", "hashPath": "newtonsoft.json.12.0.2.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Pomelo.EntityFrameworkCore.MySql/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-cm43LBLO2/RWn4fniC/JFpqLy6deHbFK7oBOryzAkjl67UJotmQYXf4I+cjCFBLEApdxtLboBovZ+ckbGYZzjQ==", "path": "pomelo.entityframeworkcore.mysql/3.1.0", "hashPath": "pomelo.entityframeworkcore.mysql.3.1.0.nupkg.sha512"}, "Pomelo.JsonObject/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-VHPk3Yf7nDt+tZMC1M4oAoc3bgTYsOrap3VTjn//vd91b/nfquAbAeq1k0Lf7mPt8J7imLd9Pbzm50uB5euuZA==", "path": "pomelo.jsonobject/2.2.1", "hashPath": "pomelo.jsonobject.2.2.1.nupkg.sha512"}, "runtime.any.System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-23g6rqftKmovn2cLeGsuHUYm0FD7pdutb0uQMJpZ3qTvq+zHkgmt6J65VtRry4WDGYlmkMa4xDACtaQ94alNag==", "path": "runtime.any.system.collections/4.3.0", "hashPath": "runtime.any.system.collections.4.3.0.nupkg.sha512"}, "runtime.any.System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-S/GPBmfPBB48ZghLxdDR7kDAJVAqgAuThyDJho3OLP5OS4tWD2ydyL8LKm8lhiBxce10OKe9X2zZ6DUjAqEbPg==", "path": "runtime.any.system.diagnostics.tools/4.3.0", "hashPath": "runtime.any.system.diagnostics.tools.4.3.0.nupkg.sha512"}, "runtime.any.System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1lpifymjGDzoYIaam6/Hyqf8GhBI3xXYLK2TgEvTtuZMorG3Kb9QnMTIKhLjJYXIiu1JvxjngHvtVFQQlpQ3HQ==", "path": "runtime.any.system.diagnostics.tracing/4.3.0", "hashPath": "runtime.any.system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "runtime.any.System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sMDBnad4rp4t7GY442Jux0MCUuKL4otn5BK6Ni0ARTXTSpRNBzZ7hpMfKSvnVSED5kYJm96YOWsqV0JH0d2uuw==", "path": "runtime.any.system.globalization/4.3.0", "hashPath": "runtime.any.system.globalization.4.3.0.nupkg.sha512"}, "runtime.any.System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-M1r+760j1CNA6M/ZaW6KX8gOS8nxPRqloqDcJYVidRG566Ykwcs29AweZs2JF+nMOCgWDiMfPSTMfvwOI9F77w==", "path": "runtime.any.system.globalization.calendars/4.3.0", "hashPath": "runtime.any.system.globalization.calendars.4.3.0.nupkg.sha512"}, "runtime.any.System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SDZ5AD1DtyRoxYtEcqQ3HDlcrorMYXZeCt7ZhG9US9I5Vva+gpIWDGMkcwa5XiKL0ceQKRZIX2x0XEjLX7PDzQ==", "path": "runtime.any.system.io/4.3.0", "hashPath": "runtime.any.system.io.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hLC3A3rI8jipR5d9k7+f0MgRCW6texsAp0MWkN/ci18FMtQ9KH7E2vDn/DH2LkxsszlpJpOn9qy6Z6/69rH6eQ==", "path": "runtime.any.system.reflection/4.3.0", "hashPath": "runtime.any.system.reflection.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cPhT+Vqu52+cQQrDai/V91gubXUnDKNRvlBnH+hOgtGyHdC17aQIU64EaehwAQymd7kJA5rSrVRNfDYrbhnzyA==", "path": "runtime.any.system.reflection.extensions/4.3.0", "hashPath": "runtime.any.system.reflection.extensions.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nrm1p3armp6TTf2xuvaa+jGTTmncALWFq22CpmwRvhDf6dE9ZmH40EbOswD4GnFLrMRS0Ki6Kx5aUPmKK/hZBg==", "path": "runtime.any.system.reflection.primitives/4.3.0", "hashPath": "runtime.any.system.reflection.primitives.4.3.0.nupkg.sha512"}, "runtime.any.System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lxb89SMvf8w9p9+keBLyL6H6x/TEmc6QVsIIA0T36IuyOY3kNvIdyGddA2qt35cRamzxF8K5p0Opq4G4HjNbhQ==", "path": "runtime.any.system.resources.resourcemanager/4.3.0", "hashPath": "runtime.any.system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fRS7zJgaG9NkifaAxGGclDDoRn9HC7hXACl52Or06a/fxdzDajWb5wov3c6a+gVSlekRoexfjwQSK9sh5um5LQ==", "path": "runtime.any.system.runtime/4.3.0", "hashPath": "runtime.any.system.runtime.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GG84X6vufoEzqx8PbeBKheE4srOhimv+yLtGb/JkR3Y2FmoqmueLNFU4Xx8Y67plFpltQSdK74x0qlEhIpv/CQ==", "path": "runtime.any.system.runtime.handles/4.3.0", "hashPath": "runtime.any.system.runtime.handles.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lBoFeQfxe/4eqjPi46E0LU/YaCMdNkQ8B4MZu/mkzdIAZh8RQ1NYZSj0egrQKdgdvlPFtP4STtob40r4o2DBAw==", "path": "runtime.any.system.runtime.interopservices/4.3.0", "hashPath": "runtime.any.system.runtime.interopservices.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+ihI5VaXFCMVPJNstG4O4eo1CfbrByLxRrQQTqOTp1ttK0kUKDqOdBSTaCB2IBk/QtjDrs6+x4xuezyMXdm0HQ==", "path": "runtime.any.system.text.encoding/4.3.0", "hashPath": "runtime.any.system.text.encoding.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NLrxmLsfRrOuVqPWG+2lrQZnE53MLVeo+w9c54EV+TUo4c8rILpsDXfY8pPiOy9kHpUHHP07ugKmtsU3vVW5Jg==", "path": "runtime.any.system.text.encoding.extensions/4.3.0", "hashPath": "runtime.any.system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "runtime.any.System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OhBAVBQG5kFj1S+hCEQ3TUHBAEtZ3fbEMgZMRNdN8A0Pj4x+5nTELEqL59DU0TjKVE6II3dqKw4Dklb3szT65w==", "path": "runtime.any.system.threading.tasks/4.3.0", "hashPath": "runtime.any.system.threading.tasks.4.3.0.nupkg.sha512"}, "runtime.any.System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-w4ehZJ+AwXYmGwYu+rMvym6RvMaRiUEQR1u6dwcyuKHxz8Heu/mO9AG1MquEgTyucnhv3M43X0iKpDOoN17C0w==", "path": "runtime.any.system.threading.timer/4.3.0", "hashPath": "runtime.any.system.threading.timer.4.3.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.unix.Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2mI2Mfq+CVatgr4RWGvAWBjoCfUafy6VNFU7G9OA52DjO8x/okfIbsEq2UPgeGfdpO7X5gmPXKT8slx0tn0Mhw==", "path": "runtime.unix.microsoft.win32.primitives/4.3.0", "hashPath": "runtime.unix.microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "runtime.unix.System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JSEiU9EvE2vJTHUuHnSg9le8XDbvZmjZ/3PhLviICzY1TTDE7c/uNYVtE9qTA9PAOZsqccy5lxvfaZOeBhT3tA==", "path": "runtime.unix.system.console/4.3.0", "hashPath": "runtime.unix.system.console.4.3.0.nupkg.sha512"}, "runtime.unix.System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-WV8KLRHWVUVUDduFnvGMHt0FsEt2wK6xPl1EgDKlaMx2KnZ43A/O0GzP8wIuvAC7mq4T9V1mm90r+PXkL9FPdQ==", "path": "runtime.unix.system.diagnostics.debug/4.3.0", "hashPath": "runtime.unix.system.diagnostics.debug.4.3.0.nupkg.sha512"}, "runtime.unix.System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ajmTcjrqc3vgV1TH54DRioshbEniaFbOAJ0kReGuNsp9uIcqYle0RmUo6+Qlwqe3JIs4TDxgnqs3UzX3gRJ1rA==", "path": "runtime.unix.system.io.filesystem/4.3.0", "hashPath": "runtime.unix.system.io.filesystem.4.3.0.nupkg.sha512"}, "runtime.unix.System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AZcRXhH7Gamr+bckUfX3iHefPIrujJTt9XWQWo0elNiP1SNasX0KBWINZkDKY0GsOrsyJ7cB4MgIRTZzLlsTKg==", "path": "runtime.unix.system.net.primitives/4.3.0", "hashPath": "runtime.unix.system.net.primitives.4.3.0.nupkg.sha512"}, "runtime.unix.System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-4NcLbqajFaD3PvhOdmbieeBlKY4d8/kBfgJ5g28n6k1jWEICabvLM62gvmUS/CvyfvcZxVanKPl+E9LhPzfXZw==", "path": "runtime.unix.system.net.sockets/4.3.0", "hashPath": "runtime.unix.system.net.sockets.4.3.0.nupkg.sha512"}, "runtime.unix.System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ooWzobr5RAq34r9uan1r/WPXJYG1XWy9KanrxNvEnBzbFdQbMG7Y3bVi4QxR7xZMNLOxLLTAyXvnSkfj5boZSg==", "path": "runtime.unix.system.private.uri/4.3.0", "hashPath": "runtime.unix.system.private.uri.4.3.0.nupkg.sha512"}, "runtime.unix.System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-zQiTBVpiLftTQZW8GFsV0gjYikB1WMkEPIxF5O6RkUrSV/OgvRRTYgeFQha/0keBpuS0HYweraGRwhfhJ7dj7w==", "path": "runtime.unix.system.runtime.extensions/4.3.0", "hashPath": "runtime.unix.system.runtime.extensions.4.3.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/5.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-UkL9GU0mfaA+7RwYjEaBFvAzL8qNQhNqAeV5uaWUu/Z+fVgvK9FHkGCpTXBqSQeIHuZaIElzxnLDdIqGzuCnVg==", "path": "swashbuckle.aspnetcore/5.6.3", "hashPath": "swashbuckle.aspnetcore.5.6.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/5.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-rn/MmLscjg6WSnTZabojx5DQYle2GjPanSPbCU3Kw8Hy72KyQR3uy8R1Aew5vpNALjfUFm2M/vwUtqdOlzw+GA==", "path": "swashbuckle.aspnetcore.swagger/5.6.3", "hashPath": "swashbuckle.aspnetcore.swagger.5.6.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/5.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-CkhVeod/iLd3ikVTDOwG5sym8BE5xbqGJ15iF3cC7ZPg2kEwDQL4a88xjkzsvC9oOB2ax6B0rK0EgRK+eOBX+w==", "path": "swashbuckle.aspnetcore.swaggergen/5.6.3", "hashPath": "swashbuckle.aspnetcore.swaggergen.5.6.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/5.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-BPvcPxQRMsYZ3HnYmGKRWDwX4Wo29WHh14Q6B10BB8Yfbbcza+agOC2UrBFA1EuaZuOsFLbp6E2+mqVNF/Je8A==", "path": "swashbuckle.aspnetcore.swaggerui/5.6.3", "hashPath": "swashbuckle.aspnetcore.swaggerui.5.6.3.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ratu44uTIHgeBeI0dE8DWvmXVBSo4u7ozRZZHOMmK/JPpYyo0dAfgSiHlpiObMQ5lEtEyIXA40sKRYg5J6A8uQ==", "path": "system.buffers/4.3.0", "hashPath": "system.buffers.4.3.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-RVSM6wZUo6L2y6P3vN6gjUtyJ2IF2RVtrepF3J7nrDKfFQd5u/SnSUFclchYQis8/k5scHy9E+fVeKVQLnnkzw==", "path": "system.collections.immutable/1.7.0", "hashPath": "system.collections.immutable.1.7.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-oJjw3uFuVDJiJNbCD8HB4a2p3NYLdt1fiT5OGsPLw+WTOuG0KpP4OXelMmmVKpClueMsit6xOlzy4wNKQFiBLg==", "path": "system.diagnostics.diagnosticsource/4.7.0", "hashPath": "system.diagnostics.diagnosticsource.4.7.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.NameResolution/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AFYl08R7MrsrEjqpQWTZWBadqXyTzNDaWpMqyxhb0d6sGhV6xMDKueuBXlLL30gz+DIRY6MpdgnHWlCh5wmq9w==", "path": "system.net.nameresolution/4.3.0", "hashPath": "system.net.nameresolution.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4SwANiUGho1esj4V4oSlPllXjzCZDE+5XXso2P03LW2vOda2Enzh8DWOxwN6hnrJyp314c7KuVu31QYhRzOGg==", "path": "system.private.uri/4.3.0", "hashPath": "system.private.uri.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HVL1rvqYtnRCxFsYag/2le/ZfKLK4yMw79+s6FmKXbSCNN0JeAhrYxnRAHFoWRa0dEojsDcbBSpH3l22QxAVyw==", "path": "system.security.principal.windows/4.3.0", "hashPath": "system.security.principal.windows.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-npvJkVKl5rKXrtl1Kkm6OhOUaYGEiF9wFbppFRWSMoApKzt2PiPHT2Bb8a5sAWxprvdOAtvaARS9QYMznEUtug==", "path": "system.threading.tasks.extensions/4.3.0", "hashPath": "system.threading.tasks.extensions.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Antiforgery/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Forms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Server/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.CookiePolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.Internal/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HostFiltering/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpOverrides/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Identity/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Metadata/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Rewrite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IIS/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Session/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebUtilities/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.CSharp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.FileExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Ini/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Composite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Embedded/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Physical/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileSystemGlobbing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Stores/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ObjectPool/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.ConfigurationExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.WebEncoders/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.JSInterop/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Net.Http.Headers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic.Core/10.0.5.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/2.1.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Buffers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent.Reference/4.0.15.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Immutable.Reference/1.2.5.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Annotations.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Brotli/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipelines/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Memory/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.HttpListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Mail/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServicePoint/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebClient/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics.Vectors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.DispatchProxy/4.0.6.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Metadata/1.4.5.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.TypeExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.Unsafe/4.0.6.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.WindowsRuntime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Intrinsics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Loader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/4.1.5.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Cng.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Permissions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Windows.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.CodePages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encodings.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Channels/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Dataflow/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions.Local/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ValueTuple/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web.HttpUtility/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}}, "runtimes": {"alpine-x64": ["alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.10-x64": ["alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.11-x64": ["alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.12-x64": ["alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.13-x64": ["alpine.3.13", "alpine.3.12-x64", "alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.14-x64": ["alpine.3.14", "alpine.3.13-x64", "alpine.3.13", "alpine.3.12-x64", "alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.15-x64": ["alpine.3.15", "alpine.3.14-x64", "alpine.3.14", "alpine.3.13-x64", "alpine.3.13", "alpine.3.12-x64", "alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.16-x64": ["alpine.3.16", "alpine.3.15-x64", "alpine.3.15", "alpine.3.14-x64", "alpine.3.14", "alpine.3.13-x64", "alpine.3.13", "alpine.3.12-x64", "alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.17-x64": ["alpine.3.17", "alpine.3.16-x64", "alpine.3.16", "alpine.3.15-x64", "alpine.3.15", "alpine.3.14-x64", "alpine.3.14", "alpine.3.13-x64", "alpine.3.13", "alpine.3.12-x64", "alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.18-x64": ["alpine.3.18", "alpine.3.17-x64", "alpine.3.17", "alpine.3.16-x64", "alpine.3.16", "alpine.3.15-x64", "alpine.3.15", "alpine.3.14-x64", "alpine.3.14", "alpine.3.13-x64", "alpine.3.13", "alpine.3.12-x64", "alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.6-x64": ["alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.7-x64": ["alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.8-x64": ["alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.9-x64": ["alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android-x64": ["android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.21-x64": ["android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.22-x64": ["android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.23-x64": ["android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.24-x64": ["android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.25-x64": ["android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.26-x64": ["android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.27-x64": ["android.27", "android.26-x64", "android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.28-x64": ["android.28", "android.27-x64", "android.27", "android.26-x64", "android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.29-x64": ["android.29", "android.28-x64", "android.28", "android.27-x64", "android.27", "android.26-x64", "android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.30-x64": ["android.30", "android.29-x64", "android.29", "android.28-x64", "android.28", "android.27-x64", "android.27", "android.26-x64", "android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.31-x64": ["android.31", "android.30-x64", "android.30", "android.29-x64", "android.29", "android.28-x64", "android.28", "android.27-x64", "android.27", "android.26-x64", "android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.32-x64": ["android.32", "android.31-x64", "android.31", "android.30-x64", "android.30", "android.29-x64", "android.29", "android.28-x64", "android.28", "android.27-x64", "android.27", "android.26-x64", "android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "arch-x64": ["arch", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "centos-x64": ["centos", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "centos.7-x64": ["centos.7", "centos-x64", "rhel.7-x64", "centos", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "centos.8-x64": ["centos.8", "centos-x64", "rhel.8-x64", "centos", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "centos.9-x64": ["centos.9", "centos-x64", "rhel.9-x64", "centos", "rhel.9", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "debian-x64": ["debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "debian.10-x64": ["debian.10", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "debian.11-x64": ["debian.11", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "debian.12-x64": ["debian.12", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "debian.8-x64": ["debian.8", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "debian.9-x64": ["debian.9", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "exherbo-x64": ["exherbo", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora-x64": ["fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.23-x64": ["fedora.23", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.24-x64": ["fedora.24", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.25-x64": ["fedora.25", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.26-x64": ["fedora.26", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.27-x64": ["fedora.27", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.28-x64": ["fedora.28", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.29-x64": ["fedora.29", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.30-x64": ["fedora.30", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.31-x64": ["fedora.31", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.32-x64": ["fedora.32", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.33-x64": ["fedora.33", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.34-x64": ["fedora.34", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.35-x64": ["fedora.35", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.36-x64": ["fedora.36", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.37-x64": ["fedora.37", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.38-x64": ["fedora.38", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.39-x64": ["fedora.39", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "gentoo-x64": ["gentoo", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-bionic-x64": ["linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-musl-x64": ["linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-x64": ["linux", "unix-x64", "unix", "any", "base"], "linuxmint.17-x64": ["linuxmint.17", "ubuntu.14.04-x64", "ubuntu.14.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.17.1-x64": ["linuxmint.17.1", "linuxmint.17-x64", "linuxmint.17", "ubuntu.14.04-x64", "ubuntu.14.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.17.2-x64": ["linuxmint.17.2", "linuxmint.17.1-x64", "linuxmint.17.1", "linuxmint.17-x64", "linuxmint.17", "ubuntu.14.04-x64", "ubuntu.14.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.17.3-x64": ["linuxmint.17.3", "linuxmint.17.2-x64", "linuxmint.17.2", "linuxmint.17.1-x64", "linuxmint.17.1", "linuxmint.17-x64", "linuxmint.17", "ubuntu.14.04-x64", "ubuntu.14.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.18-x64": ["linuxmint.18", "ubuntu.16.04-x64", "ubuntu.16.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.18.1-x64": ["linuxmint.18.1", "linuxmint.18-x64", "linuxmint.18", "ubuntu.16.04-x64", "ubuntu.16.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.18.2-x64": ["linuxmint.18.2", "linuxmint.18.1-x64", "linuxmint.18.1", "linuxmint.18-x64", "linuxmint.18", "ubuntu.16.04-x64", "ubuntu.16.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.18.3-x64": ["linuxmint.18.3", "linuxmint.18.2-x64", "linuxmint.18.2", "linuxmint.18.1-x64", "linuxmint.18.1", "linuxmint.18-x64", "linuxmint.18", "ubuntu.16.04-x64", "ubuntu.16.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.19-x64": ["linuxmint.19", "ubuntu.18.04-x64", "ubuntu.18.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.19.1-x64": ["linuxmint.19.1", "linuxmint.19-x64", "linuxmint.19", "ubuntu.18.04-x64", "ubuntu.18.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.19.2-x64": ["linuxmint.19.2", "linuxmint.19.1-x64", "linuxmint.19.1", "linuxmint.19-x64", "linuxmint.19", "ubuntu.18.04-x64", "ubuntu.18.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "manjaro-x64": ["manjaro", "arch-x64", "arch", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "miraclelinux-x64": ["miracle<PERSON><PERSON>", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "miraclelinux.8-x64": ["miraclelinux.8", "miraclelinux-x64", "rhel.8-x64", "miracle<PERSON><PERSON>", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "miraclelinux.9-x64": ["miraclelinux.9", "miraclelinux-x64", "rhel.9-x64", "miracle<PERSON><PERSON>", "rhel.9", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol-x64": ["ol", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7-x64": ["ol.7", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.0-x64": ["ol.7.0", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.1-x64": ["ol.7.1", "ol.7.0-x64", "rhel.7.1-x64", "ol.7.0", "rhel.7.1", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.2-x64": ["ol.7.2", "ol.7.1-x64", "rhel.7.2-x64", "ol.7.1", "rhel.7.2", "ol.7.0-x64", "rhel.7.1-x64", "ol.7.0", "rhel.7.1", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.3-x64": ["ol.7.3", "ol.7.2-x64", "rhel.7.3-x64", "ol.7.2", "rhel.7.3", "ol.7.1-x64", "rhel.7.2-x64", "ol.7.1", "rhel.7.2", "ol.7.0-x64", "rhel.7.1-x64", "ol.7.0", "rhel.7.1", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.4-x64": ["ol.7.4", "ol.7.3-x64", "rhel.7.4-x64", "ol.7.3", "rhel.7.4", "ol.7.2-x64", "rhel.7.3-x64", "ol.7.2", "rhel.7.3", "ol.7.1-x64", "rhel.7.2-x64", "ol.7.1", "rhel.7.2", "ol.7.0-x64", "rhel.7.1-x64", "ol.7.0", "rhel.7.1", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.5-x64": ["ol.7.5", "ol.7.4-x64", "rhel.7.5-x64", "ol.7.4", "rhel.7.5", "ol.7.3-x64", "rhel.7.4-x64", "ol.7.3", "rhel.7.4", "ol.7.2-x64", "rhel.7.3-x64", "ol.7.2", "rhel.7.3", "ol.7.1-x64", "rhel.7.2-x64", "ol.7.1", "rhel.7.2", "ol.7.0-x64", "rhel.7.1-x64", "ol.7.0", "rhel.7.1", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.6-x64": ["ol.7.6", "ol.7.5-x64", "rhel.7.6-x64", "ol.7.5", "rhel.7.6", "ol.7.4-x64", "rhel.7.5-x64", "ol.7.4", "rhel.7.5", "ol.7.3-x64", "rhel.7.4-x64", "ol.7.3", "rhel.7.4", "ol.7.2-x64", "rhel.7.3-x64", "ol.7.2", "rhel.7.3", "ol.7.1-x64", "rhel.7.2-x64", "ol.7.1", "rhel.7.2", "ol.7.0-x64", "rhel.7.1-x64", "ol.7.0", "rhel.7.1", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.8-x64": ["ol.8", "ol-x64", "rhel.8-x64", "ol", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.8.0-x64": ["ol.8.0", "ol.8-x64", "rhel.8.0-x64", "ol.8", "rhel.8.0", "ol-x64", "rhel.8-x64", "ol", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse-x64": ["opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse.13.2-x64": ["opensuse.13.2", "opensuse-x64", "opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse.15.0-x64": ["opensuse.15.0", "opensuse-x64", "opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse.15.1-x64": ["opensuse.15.1", "opensuse-x64", "opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse.42.1-x64": ["opensuse.42.1", "opensuse-x64", "opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse.42.2-x64": ["opensuse.42.2", "opensuse-x64", "opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse.42.3-x64": ["opensuse.42.3", "opensuse-x64", "opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel-x64": ["rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.6-x64": ["rhel.6", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7-x64": ["rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.0-x64": ["rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.1-x64": ["rhel.7.1", "rhel.7.0-x64", "rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.2-x64": ["rhel.7.2", "rhel.7.1-x64", "rhel.7.1", "rhel.7.0-x64", "rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.3-x64": ["rhel.7.3", "rhel.7.2-x64", "rhel.7.2", "rhel.7.1-x64", "rhel.7.1", "rhel.7.0-x64", "rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.4-x64": ["rhel.7.4", "rhel.7.3-x64", "rhel.7.3", "rhel.7.2-x64", "rhel.7.2", "rhel.7.1-x64", "rhel.7.1", "rhel.7.0-x64", "rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.5-x64": ["rhel.7.5", "rhel.7.4-x64", "rhel.7.4", "rhel.7.3-x64", "rhel.7.3", "rhel.7.2-x64", "rhel.7.2", "rhel.7.1-x64", "rhel.7.1", "rhel.7.0-x64", "rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.6-x64": ["rhel.7.6", "rhel.7.5-x64", "rhel.7.5", "rhel.7.4-x64", "rhel.7.4", "rhel.7.3-x64", "rhel.7.3", "rhel.7.2-x64", "rhel.7.2", "rhel.7.1-x64", "rhel.7.1", "rhel.7.0-x64", "rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.8-x64": ["rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.8.0-x64": ["rhel.8.0", "rhel.8-x64", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.8.1-x64": ["rhel.8.1", "rhel.8.0-x64", "rhel.8.0", "rhel.8-x64", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.9-x64": ["rhel.9", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rocky-x64": ["rocky", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rocky.8-x64": ["rocky.8", "rocky-x64", "rhel.8-x64", "rocky", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rocky.9-x64": ["rocky.9", "rocky-x64", "rhel.9-x64", "rocky", "rhel.9", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles-x64": ["sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.12-x64": ["sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.12.1-x64": ["sles.12.1", "sles.12-x64", "sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.12.2-x64": ["sles.12.2", "sles.12.1-x64", "sles.12.1", "sles.12-x64", "sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.12.3-x64": ["sles.12.3", "sles.12.2-x64", "sles.12.2", "sles.12.1-x64", "sles.12.1", "sles.12-x64", "sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.12.4-x64": ["sles.12.4", "sles.12.3-x64", "sles.12.3", "sles.12.2-x64", "sles.12.2", "sles.12.1-x64", "sles.12.1", "sles.12-x64", "sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.15-x64": ["sles.15", "sles.12.4-x64", "sles.12.4", "sles.12.3-x64", "sles.12.3", "sles.12.2-x64", "sles.12.2", "sles.12.1-x64", "sles.12.1", "sles.12-x64", "sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.15.1-x64": ["sles.15.1", "sles.15-x64", "sles.15", "sles.12.4-x64", "sles.12.4", "sles.12.3-x64", "sles.12.3", "sles.12.2-x64", "sles.12.2", "sles.12.1-x64", "sles.12.1", "sles.12-x64", "sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu-x64": ["ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.14.04-x64": ["ubuntu.14.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.14.10-x64": ["ubuntu.14.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.15.04-x64": ["ubuntu.15.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.15.10-x64": ["ubuntu.15.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.16.04-x64": ["ubuntu.16.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.16.10-x64": ["ubuntu.16.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.17.04-x64": ["ubuntu.17.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.17.10-x64": ["ubuntu.17.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.18.04-x64": ["ubuntu.18.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.18.10-x64": ["ubuntu.18.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.19.04-x64": ["ubuntu.19.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.19.10-x64": ["ubuntu.19.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.20.04-x64": ["ubuntu.20.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.20.10-x64": ["ubuntu.20.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.21.04-x64": ["ubuntu.21.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.21.10-x64": ["ubuntu.21.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.22.04-x64": ["ubuntu.22.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.22.10-x64": ["ubuntu.22.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.23.04-x64": ["ubuntu.23.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.23.10-x64": ["ubuntu.23.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"]}}