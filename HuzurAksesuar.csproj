<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>


  <ItemGroup>
    <None Remove="Entity\" />
    <None Remove="Entity\DTO\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Entity\" />
    <Folder Include="Entity\DTO\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="3.1.0" />
    <PackageReference Include="ExcelDataReader" Version="3.1.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="3.1.0" />
  </ItemGroup>
</Project>
