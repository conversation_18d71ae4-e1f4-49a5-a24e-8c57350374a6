<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Entity\" />
    <None Remove="Entity\DTO\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Entity\" />
    <Folder Include="Entity\DTO\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="ExcelDataReader" Version="3.6.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.6.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.33" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.1" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="6.0.29" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.33" />
  </ItemGroup>
</Project>
