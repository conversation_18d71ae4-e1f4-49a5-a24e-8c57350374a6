{"Format": 1, "ProjectReferences": [], "MetadataReferences": [{"FilePath": "/Users/<USER>/.nuget/packages/exceldatareader.dataset/3.6.0/lib/netstandard2.0/ExcelDataReader.DataSet.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/exceldatareader/3.6.0/lib/netstandard2.0/ExcelDataReader.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/humanizer.core/2.8.26/lib/netstandard2.0/Humanizer.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Antiforgery.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Authentication.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Authentication.Cookies.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Authentication.Core.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Authentication.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Authentication.OAuth.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Authorization.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Authorization.Policy.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Components.Authorization.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Components.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Components.Forms.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Components.Server.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Components.Web.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Connections.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.CookiePolicy.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Cors.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Cryptography.Internal.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.DataProtection.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.DataProtection.Extensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Diagnostics.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Diagnostics.HealthChecks.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.HostFiltering.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Hosting.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Hosting.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Html.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Http.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Http.Connections.Common.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Http.Connections.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Http.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Http.Extensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Http.Features.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Http.Results.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.HttpLogging.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.HttpOverrides.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.HttpsPolicy.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Identity.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.jsonpatch/6.0.33/lib/net6.0/Microsoft.AspNetCore.JsonPatch.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Localization.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Localization.Routing.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Metadata.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.Core.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.Cors.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.Formatters.Json.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.Formatters.Xml.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.Localization.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.mvc.newtonsoftjson/6.0.33/lib/net6.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.Razor.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.RazorPages.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.TagHelpers.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Mvc.ViewFeatures.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Razor.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Razor.Runtime.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.ResponseCaching.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.ResponseCompression.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Rewrite.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Routing.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Routing.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Server.HttpSys.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Server.IIS.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Server.IISIntegration.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Server.Kestrel.Core.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Server.Kestrel.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.Session.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.SignalR.Common.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.SignalR.Core.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.SignalR.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.StaticFiles.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.WebSockets.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.AspNetCore.WebUtilities.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/Microsoft.CSharp.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.abstractions/6.0.33/lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.design/6.0.33/lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore/6.0.33/lib/net6.0/Microsoft.EntityFrameworkCore.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.relational/6.0.33/lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Caching.Memory.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Configuration.Binder.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Configuration.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Configuration.Ini.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Configuration.Json.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Configuration.KeyPerFile.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Configuration.Xml.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.DependencyInjection.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Features.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.FileProviders.Composite.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.FileProviders.Embedded.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Hosting.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Http.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Identity.Core.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Identity.Stores.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Localization.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Localization.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Logging.Configuration.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Logging.Console.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Logging.Debug.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Logging.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Logging.EventLog.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Logging.EventSource.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Logging.TraceSource.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.ObjectPool.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Options.DataAnnotations.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Options.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Extensions.WebEncoders.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.JSInterop.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/Microsoft.Net.Http.Headers.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.openapi/1.6.14/lib/netstandard2.0/Microsoft.OpenApi.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/Microsoft.VisualBasic.Core.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/Microsoft.VisualBasic.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/Microsoft.Win32.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/Microsoft.Win32.Registry.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/mscorlib.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/netstandard.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/newtonsoft.json.bson/1.0.2/lib/netstandard2.0/Newtonsoft.Json.Bson.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.1/lib/netstandard2.0/Newtonsoft.Json.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/npgsql/6.0.11/lib/net6.0/Npgsql.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/npgsql.entityframeworkcore.postgresql/6.0.29/lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swagger/6.8.1/lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggergen/6.8.1/lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggerui/6.8.1/lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.AppContext.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Buffers.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Collections.Concurrent.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Collections.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Collections.Immutable.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Collections.NonGeneric.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Collections.Specialized.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.ComponentModel.Annotations.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.ComponentModel.DataAnnotations.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.ComponentModel.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.ComponentModel.EventBasedAsync.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.ComponentModel.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.ComponentModel.TypeConverter.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Configuration.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Console.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Core.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Data.Common.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Data.DataSetExtensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Data.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Diagnostics.Contracts.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Diagnostics.Debug.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Diagnostics.DiagnosticSource.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/System.Diagnostics.EventLog.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Diagnostics.FileVersionInfo.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Diagnostics.Process.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Diagnostics.StackTrace.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Diagnostics.TextWriterTraceListener.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Diagnostics.Tools.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Diagnostics.TraceSource.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Diagnostics.Tracing.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Drawing.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Drawing.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Dynamic.Runtime.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Formats.Asn1.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Globalization.Calendars.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Globalization.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Globalization.Extensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.Compression.Brotli.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.Compression.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.Compression.FileSystem.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.Compression.ZipFile.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.FileSystem.AccessControl.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.FileSystem.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.FileSystem.DriveInfo.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.FileSystem.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.FileSystem.Watcher.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.IsolatedStorage.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.MemoryMappedFiles.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/System.IO.Pipelines.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.Pipes.AccessControl.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.Pipes.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.IO.UnmanagedMemoryStream.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Linq.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Linq.Expressions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Linq.Parallel.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Linq.Queryable.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Memory.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.Http.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.Http.Json.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.HttpListener.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.Mail.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.NameResolution.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.NetworkInformation.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.Ping.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.Requests.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.Security.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.ServicePoint.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.Sockets.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.WebClient.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.WebHeaderCollection.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.WebProxy.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.WebSockets.Client.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Net.WebSockets.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Numerics.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Numerics.Vectors.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.ObjectModel.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Reflection.DispatchProxy.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Reflection.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Reflection.Emit.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Reflection.Emit.ILGeneration.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Reflection.Emit.Lightweight.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Reflection.Extensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Reflection.Metadata.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Reflection.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Reflection.TypeExtensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Resources.Reader.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Resources.ResourceManager.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Resources.Writer.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.CompilerServices.VisualC.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.Extensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.Handles.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.InteropServices.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.InteropServices.RuntimeInformation.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.Intrinsics.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.Loader.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.Numerics.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.Serialization.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.Serialization.Formatters.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.Serialization.Json.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.Serialization.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Runtime.Serialization.Xml.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.AccessControl.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.Claims.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.Cryptography.Algorithms.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.Cryptography.Cng.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.Cryptography.Csp.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.Cryptography.Encoding.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.Cryptography.OpenSsl.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.Cryptography.Primitives.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.Cryptography.X509Certificates.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/ref/net6.0/System.Security.Cryptography.Xml.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.Principal.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.Principal.Windows.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Security.SecureString.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.ServiceModel.Web.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.ServiceProcess.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Text.Encoding.CodePages.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Text.Encoding.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Text.Encoding.Extensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Text.Encodings.Web.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Text.Json.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Text.RegularExpressions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Threading.Channels.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Threading.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Threading.Overlapped.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Threading.Tasks.Dataflow.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Threading.Tasks.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Threading.Tasks.Extensions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Threading.Tasks.Parallel.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Threading.Thread.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Threading.ThreadPool.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Threading.Timer.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Transactions.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Transactions.Local.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.ValueTuple.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Web.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Web.HttpUtility.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Windows.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Xml.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Xml.Linq.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Xml.ReaderWriter.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Xml.Serialization.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Xml.XDocument.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Xml.XmlDocument.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Xml.XmlSerializer.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Xml.XPath.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/System.Xml.XPath.XDocument.dll", "Aliases": [], "Framework": null}, {"FilePath": "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/ref/net6.0/WindowsBase.dll", "Aliases": [], "Framework": null}], "Files": ["/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Controllers/AdminController.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Controllers/HomeController.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/DTO/CategoryDTO.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/DTO/DetailsDTO.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/DTO/ImageDTO.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/DTO/ProductDTO.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/DTO/ProductInfoDTO.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/DTO/SliderDTO.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/ErrorViewModel.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/Exceptions.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/HCatalog.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/HCategory.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/HDbContext.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/HExcelF.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/HImage.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/HProduct.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/HProductInfo.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/HSlider.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/ViewModels/AdminProductsModel.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/ViewModels/DetailsViewModel.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/ViewModels/ProductsViewModel.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Entity/ViewModels/VideoPageModel.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Program.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Repository/CategoryRepository.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/Repository/ProductRepository.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/obj/Debug/net6.0/HuzurAksesuar.GlobalUsings.g.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/obj/Debug/net6.0/.NETCoreApp,Version=v6.0.AssemblyAttributes.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/obj/Debug/net6.0/HuzurAksesuar.AssemblyInfo.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/obj/Debug/net6.0/HuzurAksesuar.AssemblyInfo.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/obj/Debug/net6.0/HuzurAksesuar.AssemblyInfo.cs", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/obj/Debug/net6.0/HuzurAksesuar.AssemblyInfo.cs"], "BuildActions": ["Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile"], "Analyzers": ["/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk.Web/analyzers/cs/Microsoft.AspNetCore.Analyzers.dll", "/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk.Web/analyzers/cs/Microsoft.AspNetCore.Mvc.Analyzers.dll", "/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk.Web/analyzers/cs/Microsoft.AspNetCore.Components.SdkAnalyzers.dll", "/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk/analyzers/Microsoft.CodeAnalysis.CSharp.NetAnalyzers.dll", "/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk/analyzers/Microsoft.CodeAnalysis.NetAnalyzers.dll", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.analyzers/6.0.33/analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/analyzers/dotnet/cs/System.Text.Json.SourceGeneration.dll", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/analyzers/dotnet/cs/Microsoft.AspNetCore.App.Analyzers.dll", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/analyzers/dotnet/cs/Microsoft.AspNetCore.App.CodeFixes.dll", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk.Razor/source-generators/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll", "/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk.Razor/source-generators/Microsoft.AspNetCore.Razor.Language.dll", "/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk.Razor/source-generators/Microsoft.AspNetCore.Razor.Utilities.Shared.dll", "/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk.Razor/source-generators/Microsoft.CodeAnalysis.Razor.dll", "/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk.Razor/source-generators/Microsoft.Extensions.ObjectPool.dll", "/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk.Razor/source-generators/Microsoft.NET.Sdk.Razor.SourceGenerators.dll", "/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk.Razor/source-generators/System.Collections.Immutable.dll"], "AdditionalFiles": [], "EditorConfigFiles": ["/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk.Razor/source-generators/RazorSourceGenerator.razorencconfig", "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/obj/Debug/net6.0/HuzurAksesuar.GeneratedMSBuildEditorConfig.editorconfig", "/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk/analyzers/build/config/analysislevel_6_default.editorconfig"], "DefineConstants": ["TRACE", "DEBUG", "NET", "NET6_0", "NETCOREAPP", "NET5_0_OR_GREATER", "NET6_0_OR_GREATER", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "IntermediateAssembly": "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/obj/Debug/net6.0/HuzurAksesuar.dll"}