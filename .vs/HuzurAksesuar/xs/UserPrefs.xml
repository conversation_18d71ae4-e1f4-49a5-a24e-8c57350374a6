﻿<Properties StartupConfiguration="{79F4FDA9-0592-4968-B21E-D0AECBDF032D}|Default">
  <MonoDevelop.Ide.DebuggingService.PinnedWatches />
  <MonoDevelop.Ide.Workbench ActiveDocument="Controllers/AdminController.cs">
    <Files>
      <File FileName="Entity/HCatalog.cs" />
      <File FileName="Entity/HProductInfo.cs" />
      <File FileName="Entity/HImage.cs" />
      <File FileName="Controllers/HomeController.cs" Line="129" Column="29" />
      <File FileName="Repository/CategoryRepository.cs" />
      <File FileName="Repository/ProductRepository.cs" />
      <File FileName="Program.cs" Line="1" Column="1" />
      <File FileName="Startup.cs" Line="70" Column="11" />
      <File FileName="Entity/HDbContext.cs" Line="1" Column="1" />
      <File FileName="Controllers/AdminController.cs" Line="21" Column="30" />
    </Files>
    <Pads>
      <Pad Id="ProjectPad">
        <State name="__root__">
          <Node name="HuzurAksesuar" expanded="True">
            <Node name="HuzurAksesuar" expanded="True">
              <Node name="Controllers" expanded="True" />
              <Node name="Repository" expanded="True" />
            </Node>
          </Node>
        </State>
      </Pad>
    </Pads>
  </MonoDevelop.Ide.Workbench>
  <MonoDevelop.Ide.ItemProperties.HuzurAksesuar PreferredExecutionTarget="/Applications/Google Chrome.app" />
  <MonoDevelop.Ide.DebuggingService.Breakpoints>
    <BreakpointStore />
  </MonoDevelop.Ide.DebuggingService.Breakpoints>
  <MonoDevelop.Ide.Workspace ActiveConfiguration="Debug" />
  <MultiItemStartupConfigurations />
</Properties>