{"format": 1, "restore": {"/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/HuzurAksesuar.csproj": {}}, "projects": {"/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/HuzurAksesuar.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/HuzurAksesuar.csproj", "projectName": "Hu<PERSON>r<PERSON>ks<PERSON>ua<PERSON>", "projectPath": "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/HuzurAksesuar.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"/usr/local/share/dotnet/sdk/7.0.317/Sdks/Microsoft.NET.Sdk.Web/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"ExcelDataReader": {"target": "Package", "version": "[3.6.0, )"}, "ExcelDataReader.DataSet": {"target": "Package", "version": "[3.6.0, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[6.0.33, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[6.0.33, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[6.0.29, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.8.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.31, 6.0.31]"}, {"name": "Microsoft.NETCore.App.Host.osx-arm64", "version": "[6.0.31, 6.0.31]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.31, 6.0.31]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.317/RuntimeIdentifierGraph.json"}}}}}