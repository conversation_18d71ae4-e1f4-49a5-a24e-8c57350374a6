{"version": 2, "dgSpecHash": "QYPU8RDAMPUitklAgQFcywVsA2n80eNPT0y/a7G6yKgqB/5S2v12f2gz9GkxbxCB/UdPB83lMTYnhAMBzBnbzA==", "success": true, "projectFilePath": "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/HuzurAksesuar.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/exceldatareader/3.6.0/exceldatareader.3.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/exceldatareader.dataset/3.6.0/exceldatareader.dataset.3.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/humanizer.core/2.8.26/humanizer.core.2.8.26.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.jsonpatch/6.0.33/microsoft.aspnetcore.jsonpatch.6.0.33.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.mvc.newtonsoftjson/6.0.33/microsoft.aspnetcore.mvc.newtonsoftjson.6.0.33.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.csharp/4.7.0/microsoft.csharp.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore/6.0.33/microsoft.entityframeworkcore.6.0.33.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.abstractions/6.0.33/microsoft.entityframeworkcore.abstractions.6.0.33.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.analyzers/6.0.33/microsoft.entityframeworkcore.analyzers.6.0.33.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.design/6.0.33/microsoft.entityframeworkcore.design.6.0.33.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.relational/6.0.33/microsoft.entityframeworkcore.relational.6.0.33.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.apidescription.server/6.0.5/microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.abstractions/6.0.0/microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.memory/6.0.1/microsoft.extensions.caching.memory.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/6.0.0/microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/6.0.1/microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/6.0.0/microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/6.0.0/microsoft.extensions.logging.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/6.0.0/microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/6.0.0/microsoft.extensions.options.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/6.0.0/microsoft.extensions.primitives.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.openapi/1.6.14/microsoft.openapi.1.6.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.1/newtonsoft.json.13.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json.bson/1.0.2/newtonsoft.json.bson.1.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/npgsql/6.0.11/npgsql.6.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/npgsql.entityframeworkcore.postgresql/6.0.29/npgsql.entityframeworkcore.postgresql.6.0.29.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore/6.8.1/swashbuckle.aspnetcore.6.8.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swagger/6.8.1/swashbuckle.aspnetcore.swagger.6.8.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggergen/6.8.1/swashbuckle.aspnetcore.swaggergen.6.8.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggerui/6.8.1/swashbuckle.aspnetcore.swaggerui.6.8.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.immutable/6.0.0/system.collections.immutable.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/6.0.1/system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.31/microsoft.aspnetcore.app.ref.6.0.31.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.host.osx-arm64/6.0.31/microsoft.netcore.app.host.osx-arm64.6.0.31.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.31/microsoft.netcore.app.ref.6.0.31.nupkg.sha512"], "logs": []}