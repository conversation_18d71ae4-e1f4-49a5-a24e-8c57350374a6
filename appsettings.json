﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Microsoft.EntityFrameworkCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DefaultConnection": "Host=*************;Port=5432;Database=huzurdb;Username=remzi;Password=**************;"
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://0.0.0.0:5000"
      }
    }
  },
  "Swagger": {
    "Title": "HuzurAksesuar API",
    "Version": "v1",
    "Description": "Modernized .NET 6 Web API with PostgreSQL and optimized performance"
  }
}

