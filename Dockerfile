# Use the official .NET 6 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 5000

# Use the official .NET 6 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src

# Copy project file and restore dependencies
COPY ["HuzurAksesuar.csproj", "./"]
RUN dotnet restore "HuzurAksesuar.csproj"

# Copy source code and build
COPY . .
RUN dotnet build "HuzurAksesuar.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "HuzurAksesuar.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage - runtime image
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create a non-root user for security
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:5000

ENTRYPOINT ["dotnet", "HuzurAksesuar.dll"]
