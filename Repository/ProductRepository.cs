﻿using Huzur.Models;
using HuzurAksesuar.Entity.DTO;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

public class ProductRepository
{
    private readonly HDbContext _context;
    private readonly ILogger<ProductRepository> _logger;

    public ProductRepository(HDbContext context, ILogger<ProductRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    // Videoları Döndüren - Optimized with AsNoTracking
    public async Task<List<HProduct>> GetVideosAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.HProducts
                .AsNoTracking()
                .Where(h => h.promolink != null)
                .Select(h => new HProduct
                {
                    promolink = h.promolink
                })
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting videos");
            throw;
        }
    }

    // Kategori ID'ye Göre Videoları Döndüren - Optimized
    public async Task<List<HProduct>> GetVideosByCategoryIdAsync(int categoryId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.HProducts
                .AsNoTracking()
                .Where(h => h.promolink != null && h.CategoryId == categoryId)
                .Select(h => new HProduct
                {
                    id = h.id,
                    promolink = h.promolink,
                    name = h.name,
                    nameUS = h.nameUS,
                    nameAR = h.nameAR
                })
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting videos by category ID: {CategoryId}", categoryId);
            throw;
        }
    }

    // İsimle Arama Yapan - Optimized with case-insensitive search
    public async Task<List<HProduct>> SearchByNameAsync(string keyword, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
                return new List<HProduct>();

            return await _context.HProducts
                .AsNoTracking()
                .Where(h => EF.Functions.ILike(h.name, $"%{keyword}%") ||
                           EF.Functions.ILike(h.nameUS, $"%{keyword}%") ||
                           EF.Functions.ILike(h.nameAR, $"%{keyword}%"))
                .Select(h => new HProduct
                {
                    id = h.id,
                    name = h.name,
                    nameUS = h.nameUS,
                    nameAR = h.nameAR,
                    colors = h.colors,
                    Images = h.Images.Select(i => new HImage
                    {
                        id = i.id,
                        name = i.name
                    }).ToList()
                })
                .Take(50) // Limit results for performance
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while searching by name: {Keyword}", keyword);
            throw;
        }
    }

    // Popüler Ürünleri Döndüren - Optimized
    public async Task<List<HProduct>> FindByPopularTrueAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.HProducts
                .AsNoTracking()
                .Where(h => h.popular)
                .Select(h => new HProduct
                {
                    id = h.id,
                    name = h.name,
                    nameUS = h.nameUS,
                    nameAR = h.nameAR,
                    colors = h.colors,
                    Images = h.Images.Select(i => new HImage
                    {
                        id = i.id,
                        name = i.name
                    }).ToList()
                })
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting popular products");
            throw;
        }
    }

    // Ana Kategoriye Göre Popüler Ürünleri Döndüren - Optimized
    public async Task<List<HProduct>> GetPopularByMainCategoryAsync(HProduct.maincat mainct, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.HProducts
                .AsNoTracking()
                .Where(p => p.popular && p.mainct == mainct)
                .Select(p => new HProduct
                {
                    id = p.id,
                    name = p.name,
                    nameUS = p.nameUS,
                    nameAR = p.nameAR,
                    colors = p.colors,
                    Images = p.Images.Select(i => new HImage
                    {
                        id = i.id,
                        name = i.name
                    }).ToList()
                })
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting popular products by main category: {MainCategory}", mainct);
            throw;
        }
    }

    // Ürün sayısını döndüren - Async optimized
    public async Task<int> GetProductsCountAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.HProducts
                .AsNoTracking()
                .CountAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting products count");
            throw;
        }
    }

    // Tüm Ürünleri Döndüren - Optimized with pagination support
    public async Task<List<HProduct>> GetProductsAsync(int skip = 0, int take = 100, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.HProducts
                .AsNoTracking()
                .OrderBy(p => p.id) // Add ordering for consistent pagination
                .Skip(skip)
                .Take(take)
                .Select(p => new HProduct
                {
                    id = p.id,
                    name = p.name,
                    nameUS = p.nameUS,
                    nameAR = p.nameAR,
                    colors = p.colors,
                    Images = p.Images.Select(i => new HImage
                    {
                        id = i.id,
                        name = i.name
                    }).ToList()
                })
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting products with pagination. Skip: {Skip}, Take: {Take}", skip, take);
            throw;
        }
    }

    // Kategori ID'ye Göre Ürünleri Döndüren - Optimized
    public async Task<List<HProduct>> GetProductsByCategoryIdAsync(int categoryId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.HProducts
                .AsNoTracking()
                .Where(h => h.CategoryId == categoryId)
                .Select(p => new HProduct
                {
                    id = p.id,
                    name = p.name,
                    nameUS = p.nameUS,
                    nameAR = p.nameAR,
                    colors = p.colors,
                    Images = p.Images.Select(i => new HImage
                    {
                        id = i.id,
                        name = i.name
                    }).ToList()
                })
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting products by category ID: {CategoryId}", categoryId);
            throw;
        }
    }

    // Tüm Slider'ları Döndüren - Optimized
    public async Task<List<HSlider>> GetAllSlidersAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.HSliders
                .AsNoTracking()
                .OrderBy(s => s.id)
                .Select(p => new HSlider
                {
                    id = p.id,
                    upperText = p.upperText,
                    upperTextEn = p.upperTextEn,
                    upperTextAr = p.upperTextAr,
                    lowerText = p.lowerText,
                    lowerTextAr = p.lowerTextAr,
                    lowerTextEn = p.lowerTextEn,
                    midText = p.midText,
                    midTextAr = p.midTextAr,
                    midTextEn = p.midTextEn,
                    CategoryId = p.CategoryId,
                    sliderImage = p.sliderImage
                })
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting all sliders");
            throw;
        }
    }

    // Kategori ID'ye Göre Kategori Döndüren - Optimized
    public async Task<HCategory?> GetCategoryByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.HCategories
                .AsNoTracking()
                .FirstOrDefaultAsync(h => h.id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting category by ID: {CategoryId}", id);
            throw;
        }
    }

    // Tüm Kategorileri Döndüren - Optimized
    public async Task<List<CategoryDTO>> GetAllCategoriesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.HCategories
                .AsNoTracking()
                .Select(c => new CategoryDTO
                {
                    Id = c.id,
                    Name = c.name,
                    productCount = c.Products.Count()
                })
                .OrderBy(c => c.Name)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting all categories");
            throw;
        }
    }

    // Kategori ID'ye Göre Kategori Bulma - Optimized (Duplicate method removed, use GetCategoryByIdAsync instead)
    [Obsolete("Use GetCategoryByIdAsync instead")]
    public async Task<HCategory?> FindByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await GetCategoryByIdAsync(id, cancellationToken);
    }

    // Ürün ID'ye Göre Ürün Döndüren - Optimized
    public async Task<HProduct?> GetProductByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.HProducts
                .AsNoTracking()
                .Where(p => p.id == id)
                .Select(p => new HProduct
                {
                    id = p.id,
                    name = p.name,
                    nameUS = p.nameUS,
                    nameAR = p.nameAR,
                    colors = p.colors,
                    Images = p.Images.Select(i => new HImage
                    {
                        id = i.id,
                        name = i.name
                    }).ToList(),
                    ProductInfos = p.ProductInfos,
                    CategoryId = p.CategoryId
                })
                .FirstOrDefaultAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting product by ID: {ProductId}", id);
            throw;
        }
    }

    // Kategori ID'ye Göre Sınırlı Ürün Döndüren - Optimized
    public async Task<List<HProduct>> GetProductsByCategoryIdLimitedAsync(int categoryId, int limit = 5, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.HProducts
                .AsNoTracking()
                .Where(p => p.CategoryId == categoryId)
                .OrderBy(p => p.id) // Add ordering for consistent results
                .Select(p => new HProduct
                {
                    id = p.id,
                    name = p.name,
                    nameUS = p.nameUS,
                    nameAR = p.nameAR,
                    colors = p.colors,
                    Images = p.Images.Select(i => new HImage
                    {
                        id = i.id,
                        name = i.name
                    }).ToList()
                })
                .Take(limit)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting limited products by category ID: {CategoryId}, Limit: {Limit}", categoryId, limit);
            throw;
        }
    }

    // Backward compatibility methods (marked as obsolete)
    [Obsolete("Use GetVideosAsync instead")]
    public async Task<List<HProduct>> GetVideos() => await GetVideosAsync();

    [Obsolete("Use GetVideosByCategoryIdAsync instead")]
    public async Task<List<HProduct>> GetVideosByCategoryId(int categoryId) => await GetVideosByCategoryIdAsync(categoryId);

    [Obsolete("Use SearchByNameAsync instead")]
    public async Task<List<HProduct>> SearchByName(string keyword) => await SearchByNameAsync(keyword);

    [Obsolete("Use FindByPopularTrueAsync instead")]
    public async Task<List<HProduct>> FindByPopularTrue() => await FindByPopularTrueAsync();

    [Obsolete("Use GetProductsCountAsync instead")]
    public int GetProductsCount() => GetProductsCountAsync().Result;

    [Obsolete("Use GetProductsAsync instead")]
    public async Task<List<HProduct>> GetProducts() => await GetProductsAsync();

    [Obsolete("Use GetProductsByCategoryIdAsync instead")]
    public async Task<List<HProduct>> GetProductsByCategoryId(int categoryId) => await GetProductsByCategoryIdAsync(categoryId);

    [Obsolete("Use GetAllSlidersAsync instead")]
    public async Task<List<HSlider>> GetAllSliders() => await GetAllSlidersAsync();

    [Obsolete("Use GetCategoryByIdAsync instead")]
    public async Task<HCategory> GetCategoryById(int id) => await GetCategoryByIdAsync(id) ?? new HCategory();

    [Obsolete("Use GetAllCategoriesAsync instead")]
    public async Task<List<CategoryDTO>> GetAllCategories() => await GetAllCategoriesAsync();

    [Obsolete("Use GetCategoryByIdAsync instead")]
    public async Task<HCategory> FindById(int id) => await GetCategoryByIdAsync(id) ?? new HCategory();

    [Obsolete("Use GetProductByIdAsync instead")]
    public async Task<HProduct> GetProductById(int id) => await GetProductByIdAsync(id) ?? new HProduct();

    [Obsolete("Use GetProductsByCategoryIdLimitedAsync instead")]
    public async Task<List<HProduct>> GetProductByCtId(int id) => await GetProductsByCategoryIdLimitedAsync(id);

    [Obsolete("Use GetPopularByMainCategoryAsync instead")]
    public async Task<List<HProduct>> GetPopularByMainCategory(HProduct.maincat mainct) => await GetPopularByMainCategoryAsync(mainct);
}
