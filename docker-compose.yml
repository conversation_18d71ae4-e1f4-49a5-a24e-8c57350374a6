version: '3.8'

services:
  huzuraksesuar-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=huzurdb;Username=postgres;Password=your_secure_password;Include Error Detail=true;Pooling=true;Minimum Pool Size=1;Maximum Pool Size=20;
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - huzur-network

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=huzurdb
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=your_secure_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - huzur-network

volumes:
  postgres_data:

networks:
  huzur-network:
    driver: bridge
