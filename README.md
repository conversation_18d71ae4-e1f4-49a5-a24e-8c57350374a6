# HuzurAksesuar API - Modernized .NET 6

Bu proje, .NET Core 3.1'den .NET 6'ya modernize edilmiş bir Web API projesidir. PostgreSQL veritabanı kullanır ve yüksek performans için optimize edilmiştir.

## 🚀 Özellikler

- **.NET 6** - En son .NET sürümü ile geliştirildi
- **PostgreSQL** - Güçlü ve ölçeklenebilir veritabanı
- **Entity Framework Core 6.0** - Modern ORM ile veritabanı işlemleri
- **Swagger/OpenAPI** - Otomatik API dokümantasyonu
- **AsNoTracking** - Optimized read-only sorguları
- **Async/Await** - Asenkron programlama ile yüksek performans
- **Docker Ready** - Konteyner desteği
- **CORS** - Cross-origin resource sharing desteği
- **Logging** - Kapsamlı loglama sistemi

## 📋 Gereksinimler

- .NET 6.0 SDK
- PostgreSQL 12+
- Docker (opsiyonel)

## 🛠️ Kurulum

### 1. <PERSON><PERSON>

```bash
# Repository'yi klonlayın
git clone <repository-url>
cd HuzurAksesuar

# Bağımlılıkları yükleyin
dotnet restore

# Veritabanı bağlantı stringini güncelleyin
# appsettings.Development.json dosyasındaki ConnectionStrings:DefaultConnection'ı düzenleyin

# Uygulamayı çalıştırın
dotnet run
```

### 2. Docker ile Çalıştırma

```bash
# Docker Compose ile tüm servisleri başlatın
docker-compose up -d

# Logları görüntüleyin
docker-compose logs -f huzuraksesuar-api
```

## 🔧 Konfigürasyon

### Veritabanı Bağlantısı

`appsettings.json` veya environment variable ile konfigüre edilir:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=huzurdb;Username=postgres;Password=your_password;Include Error Detail=true;Pooling=true;Minimum Pool Size=1;Maximum Pool Size=20;"
  }
}
```

### Environment Variables

```bash
ConnectionStrings__DefaultConnection="Host=localhost;Port=5432;Database=huzurdb;Username=postgres;Password=your_password"
ASPNETCORE_ENVIRONMENT=Development
```

## 📚 API Endpoints

API dokümantasyonu Swagger UI üzerinden erişilebilir:
- Development: `http://localhost:5000`
- Production: `http://your-domain.com`

### Ana Endpoint'ler

- `GET /api/products` - Tüm ürünleri listele
- `GET /api/products/{id}` - Belirli bir ürünü getir
- `GET /api/products/category/{categoryId}` - Kategoriye göre ürünleri getir
- `GET /api/products/popular` - Popüler ürünleri getir
- `GET /api/categories` - Tüm kategorileri listele
- `GET /api/sliders` - Slider'ları getir

## 🚀 Performans Optimizasyonları

1. **AsNoTracking**: Read-only sorgular için EF Core tracking'i devre dışı
2. **Async/Await**: Tüm veritabanı işlemleri asenkron
3. **Query Splitting**: İlişkili veriler için sorgu bölme
4. **Connection Pooling**: Veritabanı bağlantı havuzu
5. **Indexing**: Kritik alanlar için veritabanı indeksleri
6. **Pagination**: Büyük veri setleri için sayfalama
7. **Caching**: Response caching (gerektiğinde eklenebilir)

## 🐳 Docker Deployment

### Tek Konteyner

```bash
# Image oluştur
docker build -t huzuraksesuar-api .

# Çalıştır
docker run -p 5000:5000 \
  -e ConnectionStrings__DefaultConnection="Host=your-db-host;Port=5432;Database=huzurdb;Username=postgres;Password=your_password" \
  huzuraksesuar-api
```

### Docker Compose (Önerilen)

```bash
# Tüm servisleri başlat
docker-compose up -d

# Servisleri durdur
docker-compose down

# Verileri de sil
docker-compose down -v
```

## 🔍 Monitoring ve Logging

Uygulama kapsamlı loglama sistemi içerir:

- **Console Logging**: Development ortamı için
- **File Logging**: Production ortamı için (gerektiğinde eklenebilir)
- **Error Handling**: Global exception handling
- **Database Logging**: EF Core sorgu logları

## 🧪 Testing

```bash
# Unit testleri çalıştır (test projesi eklendiğinde)
dotnet test

# Coverage raporu (gerektiğinde)
dotnet test --collect:"XPlat Code Coverage"
```

## 📝 Migration

```bash
# Yeni migration oluştur
dotnet ef migrations add MigrationName

# Veritabanını güncelle
dotnet ef database update
```

## 🔒 Güvenlik

- **CORS**: Belirli origin'lere sınırlı erişim
- **HTTPS**: Production ortamında zorunlu
- **Connection String**: Environment variable'dan okunur
- **Non-root User**: Docker konteynerinde güvenlik

## 📞 Destek

Herhangi bir sorun veya soru için lütfen issue açın.

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
